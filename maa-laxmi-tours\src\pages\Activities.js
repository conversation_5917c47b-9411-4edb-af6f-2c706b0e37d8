import React, { useState } from 'react';
import QueryForm from '../components/QueryForm';
import ModernCard from '../components/ModernCard';
import SEO from '../components/SEO';

const Activities = () => {
  const [showQueryForm, setShowQueryForm] = useState(false);
  const [selectedActivity, setSelectedActivity] = useState('');

  const activities = [
    {
      id: 1,
      title: "Scuba Diving",
      subtitle: "underwater adventure",
      price: "₹3500.00",
      duration: "3-4 Hours",
      image: "https://images.unsplash.com/photo-1544551763-77ef2d0cfc6c?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80",
      includes: "Equipment, Instructor, Underwater Photos, Certificate",
      location: "Havelock Island"
    },
    {
      id: 2,
      title: "Snorkeling",
      subtitle: "coral reef exploration",
      price: "₹1500.00",
      duration: "2-3 Hours",
      image: "https://images.unsplash.com/photo-1583212292454-1fe6229603b7?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80",
      includes: "Snorkeling Gear, Life Jacket, Guide, Refreshments",
      location: "Neil Island"
    },
    {
      id: 3,
      title: "Sea Walking",
      subtitle: "ocean floor experience",
      price: "₹4000.00",
      duration: "1 Hour",
      image: "https://images.unsplash.com/photo-1559827260-dc66d52bef19?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80",
      includes: "Special Helmet, Guide, Underwater Photos, Safety Equipment",
      location: "North Bay Island"
    },
    {
      id: 4,
      title: "Parasailing",
      subtitle: "sky high adventure",
      price: "₹2500.00",
      duration: "30 Minutes",
      image: "https://images.unsplash.com/photo-1530549387789-4c1017266635?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80",
      includes: "Safety Equipment, Professional Operator, Photos, Insurance",
      location: "Port Blair"
    },
    {
      id: 5,
      title: "Jet Skiing",
      subtitle: "speed thrills",
      price: "₹2000.00",
      duration: "20 Minutes",
      image: "https://images.unsplash.com/photo-1551698618-1dfe5d97d256?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80",
      includes: "Jet Ski, Life Jacket, Instructor, Safety Briefing",
      location: "Corbyn's Cove Beach"
    },
    {
      id: 6,
      title: "Banana Boat Ride",
      subtitle: "group fun activity",
      price: "₹800.00",
      duration: "15 Minutes",
      image: "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80",
      includes: "Banana Boat, Life Jackets, Guide, Group Fun",
      location: "Havelock Island"
    },
    {
      id: 7,
      title: "Glass Bottom Boat",
      subtitle: "marine life viewing",
      price: "₹1200.00",
      duration: "1 Hour",
      image: "https://images.unsplash.com/photo-1578662996442-48f60103fc96?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80",
      includes: "Glass Bottom Boat, Guide, Marine Life Spotting, Comfortable Seating",
      location: "North Bay Island"
    },
    {
      id: 8,
      title: "Kayaking",
      subtitle: "mangrove exploration",
      price: "₹1800.00",
      duration: "2 Hours",
      image: "https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80",
      includes: "Kayak, Paddle, Life Jacket, Guide",
      location: "Baratang Island"
    },
    {
      id: 9,
      title: "Deep Sea Fishing",
      subtitle: "fishing expedition",
      price: "₹5000.00",
      duration: "4-5 Hours",
      image: "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80",
      includes: "Fishing Equipment, Boat, Guide, Refreshments",
      location: "Port Blair"
    },
    {
      id: 10,
      title: "Sunset Cruise",
      subtitle: "romantic experience",
      price: "₹2200.00",
      duration: "2 Hours",
      image: "https://images.unsplash.com/photo-1559827260-dc66d52bef19?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80",
      includes: "Cruise, Refreshments, Music, Photography",
      location: "Port Blair"
    },
    {
      id: 11,
      title: "Trekking",
      subtitle: "nature exploration",
      price: "₹1000.00",
      duration: "3-4 Hours",
      image: "https://images.unsplash.com/photo-1578662996442-48f60103fc96?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80",
      includes: "Guide, Refreshments, First Aid, Nature Photography",
      location: "Mount Harriet"
    },
    {
      id: 12,
      title: "Island Camping",
      subtitle: "overnight adventure",
      price: "₹3000.00",
      duration: "Overnight",
      image: "https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80",
      includes: "Tent, Sleeping Bag, Meals, Bonfire",
      location: "Neil Island"
    }
  ];

  const handleBookActivity = (activityName) => {
    setSelectedActivity(activityName);
    setShowQueryForm(true);
  };

  const activityCategories = [
    { name: "Water Sports", count: 8, icon: "🏄‍♂️" },
    { name: "Underwater", count: 3, icon: "🤿" },
    { name: "Adventure", count: 4, icon: "🏔️" },
    { name: "Relaxation", count: 2, icon: "🧘‍♀️" }
  ];

  return (
    <div className="min-h-screen bg-soft-white">
      <SEO 
        title="Adventure Activities in Andaman - Water Sports & More | Maa Laxmi Tours"
        description="Experience thrilling water sports and adventure activities in Andaman including scuba diving, parasailing, jet skiing, and more. Book your adventure today!"
        keywords="Andaman water sports, scuba diving Andaman, parasailing, jet skiing, sea walking, adventure activities Andaman"
      />
      
      {/* Hero Section */}
      <section className="relative py-20 bg-gradient-to-r from-turquoise-aqua via-ocean-blue to-deep-navy text-white overflow-hidden">
        <div className="absolute inset-0 bg-black/30"></div>
        <div className="absolute top-10 left-10 w-32 h-32 bg-golden-sand/20 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-10 right-10 w-40 h-40 bg-sunset-orange/20 rounded-full blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/4 w-24 h-24 bg-golden-sand/30 rounded-full blur-2xl animate-pulse delay-500"></div>
        
        <div className="relative container mx-auto px-4 text-center">
          <div className="inline-flex items-center bg-white/20 backdrop-blur-sm rounded-full px-6 py-3 mb-6">
            <span className="text-white font-semibold">🏄‍♂️ Adventure Activities</span>
          </div>
          <h1 className="text-5xl lg:text-7xl font-bold mb-6">
            Thrilling 
            <span className="text-golden-sand"> Water</span> Adventures
          </h1>
          <p className="text-xl max-w-3xl mx-auto mb-8">
            Experience the ultimate adrenaline rush with our exciting water sports and adventure activities in the pristine waters of Andaman Islands
          </p>
          
          {/* Quick Stats */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-2xl mx-auto">
            {activityCategories.map((category, index) => (
              <div key={index} className="text-center">
                <div className="text-3xl mb-2">{category.icon}</div>
                <div className="text-2xl font-bold text-golden-sand">{category.count}+</div>
                <div className="text-sm opacity-90">{category.name}</div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Activities Grid */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <div className="inline-flex items-center bg-turquoise-aqua/10 rounded-full px-4 py-2 mb-4">
              <span className="text-turquoise-aqua font-semibold text-sm">Choose Your Adventure</span>
            </div>
            <h2 className="text-4xl lg:text-5xl font-bold text-deep-navy mb-6">
              Exciting 
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-turquoise-aqua to-ocean-blue"> Activities</span> Await
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              From underwater exploration to aerial thrills, discover the perfect activity for your adventure level
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {activities.map(activity => (
              <ModernCard
                key={activity.id}
                image={activity.image}
                duration={activity.duration}
                price={activity.price}
                title={activity.title}
                subtitle={activity.subtitle}
                includes={activity.includes}
                onBookNow={() => handleBookActivity(activity.title)}
                type="activity"
              />
            ))}
          </div>
        </div>
      </section>

      {/* Safety Information */}
      <section className="py-20 bg-gradient-to-br from-gray-50 to-blue-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-16">
              <div className="inline-flex items-center bg-sunset-orange/10 rounded-full px-4 py-2 mb-4">
                <span className="text-sunset-orange font-semibold text-sm">Safety First</span>
              </div>
              <h2 className="text-4xl font-bold text-deep-navy mb-6">Your Safety is Our Priority</h2>
              <p className="text-lg text-gray-600">Professional equipment, certified instructors, and comprehensive safety measures</p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              <div className="bg-white p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300">
                <div className="w-16 h-16 bg-gradient-to-r from-turquoise-aqua to-ocean-blue rounded-2xl flex items-center justify-center mb-6 text-white text-2xl">
                  🛡️
                </div>
                <h3 className="text-xl font-bold text-deep-navy mb-4">Certified Professionals</h3>
                <ul className="space-y-2 text-gray-600">
                  <li className="flex items-start">
                    <svg className="w-5 h-5 text-turquoise-aqua mr-2 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    All activities conducted by certified professionals
                  </li>
                  <li className="flex items-start">
                    <svg className="w-5 h-5 text-turquoise-aqua mr-2 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    International safety certifications
                  </li>
                  <li className="flex items-start">
                    <svg className="w-5 h-5 text-turquoise-aqua mr-2 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    Regular training and skill updates
                  </li>
                </ul>
              </div>

              <div className="bg-white p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300">
                <div className="w-16 h-16 bg-gradient-to-r from-sunset-orange to-golden-sand rounded-2xl flex items-center justify-center mb-6 text-white text-2xl">
                  ⚙️
                </div>
                <h3 className="text-xl font-bold text-deep-navy mb-4">Premium Equipment</h3>
                <ul className="space-y-2 text-gray-600">
                  <li className="flex items-start">
                    <svg className="w-5 h-5 text-sunset-orange mr-2 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    Top-quality safety equipment provided
                  </li>
                  <li className="flex items-start">
                    <svg className="w-5 h-5 text-sunset-orange mr-2 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    Regular maintenance and inspection
                  </li>
                  <li className="flex items-start">
                    <svg className="w-5 h-5 text-sunset-orange mr-2 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    International safety standards
                  </li>
                </ul>
              </div>

              <div className="bg-white p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300">
                <div className="w-16 h-16 bg-gradient-to-r from-golden-sand to-sunset-orange rounded-2xl flex items-center justify-center mb-6 text-white text-2xl">
                  🏥
                </div>
                <h3 className="text-xl font-bold text-deep-navy mb-4">Emergency Support</h3>
                <ul className="space-y-2 text-gray-600">
                  <li className="flex items-start">
                    <svg className="w-5 h-5 text-golden-sand mr-2 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    First aid facilities and trained staff
                  </li>
                  <li className="flex items-start">
                    <svg className="w-5 h-5 text-golden-sand mr-2 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    24/7 emergency response team
                  </li>
                  <li className="flex items-start">
                    <svg className="w-5 h-5 text-golden-sand mr-2 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    Comprehensive safety briefings
                  </li>
                </ul>
              </div>
            </div>

            <div className="mt-12 bg-gradient-to-r from-ocean-blue/10 to-turquoise-aqua/10 border border-ocean-blue/20 rounded-2xl p-8">
              <div className="flex items-start">
                <div className="flex-shrink-0">
                  <div className="w-12 h-12 bg-ocean-blue rounded-full flex items-center justify-center text-white text-xl">
                    ℹ️
                  </div>
                </div>
                <div className="ml-6">
                  <h4 className="text-xl font-bold text-deep-navy mb-2">Important Guidelines</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-gray-600">
                    <ul className="space-y-2">
                      <li>• Age and health restrictions apply for some activities</li>
                      <li>• Weather conditions may affect activity schedules</li>
                      <li>• Book activities in advance to avoid disappointment</li>
                    </ul>
                    <ul className="space-y-2">
                      <li>• Follow all safety instructions from guides</li>
                      <li>• Pregnant women should consult before participating</li>
                      <li>• Cancellation policy applies for all bookings</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-turquoise-aqua via-ocean-blue to-deep-navy text-white relative overflow-hidden">
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="absolute top-10 left-10 w-32 h-32 bg-golden-sand/20 rounded-full blur-3xl"></div>
        <div className="absolute bottom-10 right-10 w-40 h-40 bg-sunset-orange/20 rounded-full blur-3xl"></div>
        
        <div className="relative container mx-auto px-4 text-center">
          <h2 className="text-4xl lg:text-5xl font-bold mb-6">
            Ready for Your 
            <span className="text-golden-sand"> Adventure?</span>
          </h2>
          <p className="text-xl mb-10 max-w-2xl mx-auto">
            Book your favorite activities and create unforgettable memories in the crystal-clear waters of Andaman
          </p>
          
          <div className="flex flex-col sm:flex-row gap-6 justify-center">
            <button 
              onClick={() => setShowQueryForm(true)}
              className="bg-gradient-to-r from-sunset-orange to-golden-sand text-white px-10 py-4 rounded-full font-bold text-lg hover:shadow-2xl transition-all duration-300 transform hover:scale-105"
            >
              Plan My Activities
            </button>
            <a 
              href="tel:+919876543210"
              className="border-2 border-white text-white px-10 py-4 rounded-full font-bold text-lg hover:bg-white hover:text-ocean-blue transition-all duration-300"
            >
              Call Now
            </a>
          </div>
        </div>
      </section>

      {/* Query Form Modal */}
      {showQueryForm && (
        <QueryForm 
          title="Activity Booking Inquiry"
          packageName={selectedActivity ? `Activity - ${selectedActivity}` : "Multiple Activities"}
          onClose={() => setShowQueryForm(false)}
        />
      )}
    </div>
  );
};

export default Activities;