import React, { useState } from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import QueryForm from '../components/QueryForm';
import SEO from '../components/SEO';

const PackageDetail = () => {
  const { packageId } = useParams();
  const [showQueryForm, setShowQueryForm] = useState(false);
  const [activeTab, setActiveTab] = useState('itinerary');

  // Mock package data - in real app, this would come from API
  const packageData = {
    1: {
      title: "Exotic Honeymoon Holiday Package",
      subtitle: "5 Nights & 6 Days Romantic Getaway",
      price: "₹24,000",
      originalPrice: "₹30,000",
      duration: "05 Nights & 06 Days",
      image: "https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1200&q=80",
      gallery: [
        "https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80",
        "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80",
        "https://images.unsplash.com/photo-1559827260-dc66d52bef19?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80"
      ],
      includes: [
        "Accommodation in 4-star resort",
        "Daily breakfast and dinner",
        "Private cruise to Havelock Island",
        "Airport transfers in A/C cab",
        "Romantic candlelight dinner",
        "Couple spa session",
        "Professional photography session"
      ],
      excludes: [
        "Airfare to Port Blair",
        "Lunch and beverages",
        "Personal expenses",
        "Travel insurance",
        "Entry fees to monuments"
      ],
      itinerary: [
        {
          day: 1,
          title: "Arrival in Port Blair",
          description: "Arrive at Port Blair airport. Transfer to hotel. Evening visit to Corbyn's Cove Beach. Overnight stay in Port Blair.",
          activities: ["Airport pickup", "Hotel check-in", "Corbyn's Cove Beach", "Welcome dinner"]
        },
        {
          day: 2,
          title: "Port Blair Sightseeing",
          description: "Visit Cellular Jail, Anthropological Museum, and Fisheries Museum. Evening light and sound show at Cellular Jail.",
          activities: ["Cellular Jail visit", "Museum tours", "Light & Sound Show", "Local market shopping"]
        },
        {
          day: 3,
          title: "Havelock Island",
          description: "Ferry to Havelock Island. Check-in to beach resort. Visit Radhanagar Beach for sunset. Romantic dinner on beach.",
          activities: ["Ferry to Havelock", "Beach resort check-in", "Radhanagar Beach", "Romantic beach dinner"]
        },
        {
          day: 4,
          title: "Water Sports & Relaxation",
          description: "Morning water sports activities. Afternoon couple spa session. Evening at leisure on private beach.",
          activities: ["Scuba diving", "Snorkeling", "Couple spa", "Private beach time"]
        },
        {
          day: 5,
          title: "Neil Island Excursion",
          description: "Day trip to Neil Island. Visit Bharatpur Beach and Natural Bridge. Return to Havelock for overnight stay.",
          activities: ["Neil Island ferry", "Bharatpur Beach", "Natural Bridge", "Island exploration"]
        },
        {
          day: 6,
          title: "Departure",
          description: "Check-out from resort. Ferry back to Port Blair. Transfer to airport for departure.",
          activities: ["Resort check-out", "Ferry to Port Blair", "Airport transfer", "Departure"]
        }
      ],
      highlights: [
        "Stay in premium beachfront resort",
        "Private romantic dinner on beach",
        "Professional couple photography",
        "Exclusive spa treatments",
        "Water sports activities included",
        "Personal tour guide"
      ]
    }
  };

  const pkg = packageData[packageId] || packageData[1];

  return (
    <div className="min-h-screen bg-soft-white">
      <SEO 
        title={`${pkg.title} - Andaman Honeymoon Package | Maa Laxmi Tours`}
        description={`Book ${pkg.title} - ${pkg.subtitle}. ${pkg.duration} romantic getaway in Andaman with luxury accommodation and exclusive experiences.`}
      />

      {/* Hero Section */}
      <section className="relative h-96 overflow-hidden">
        <img 
          src={pkg.image} 
          alt={pkg.title}
          className="w-full h-full object-cover"
        />
        <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/30 to-transparent"></div>
        <div className="absolute bottom-8 left-8 text-white">
          <div className="inline-flex items-center bg-golden-sand text-deep-navy px-3 py-1 rounded-full text-sm font-semibold mb-4">
            {pkg.duration}
          </div>
          <h1 className="text-4xl lg:text-5xl font-bold mb-2">{pkg.title}</h1>
          <p className="text-xl opacity-90">{pkg.subtitle}</p>
        </div>
      </section>

      <div className="container mx-auto px-4 py-12">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-12">
          {/* Main Content */}
          <div className="lg:col-span-2">
            {/* Navigation Tabs */}
            <div className="flex flex-wrap gap-2 mb-8 border-b border-light-gray">
              {[
                { id: 'itinerary', label: 'Itinerary', icon: '📅' },
                { id: 'includes', label: 'Inclusions', icon: '✅' },
                { id: 'gallery', label: 'Gallery', icon: '📸' },
                { id: 'highlights', label: 'Highlights', icon: '⭐' }
              ].map(tab => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center px-6 py-3 rounded-t-lg font-semibold transition-all ${
                    activeTab === tab.id 
                      ? 'bg-ocean-blue text-white' 
                      : 'text-gray-600 hover:text-ocean-blue hover:bg-gray-50'
                  }`}
                >
                  <span className="mr-2">{tab.icon}</span>
                  {tab.label}
                </button>
              ))}
            </div>

            {/* Tab Content */}
            <div className="bg-white rounded-2xl shadow-lg p-8">
              {activeTab === 'itinerary' && (
                <div>
                  <h3 className="text-2xl font-bold text-deep-navy mb-6">Day-wise Itinerary</h3>
                  <div className="space-y-6">
                    {pkg.itinerary.map((day, index) => (
                      <div key={index} className="border-l-4 border-ocean-blue pl-6 pb-6">
                        <div className="flex items-center mb-3">
                          <div className="w-8 h-8 bg-ocean-blue text-white rounded-full flex items-center justify-center font-bold text-sm mr-3">
                            {day.day}
                          </div>
                          <h4 className="text-xl font-bold text-deep-navy">Day {day.day}: {day.title}</h4>
                        </div>
                        <p className="text-gray-600 mb-4">{day.description}</p>
                        <div className="flex flex-wrap gap-2">
                          {day.activities.map((activity, idx) => (
                            <span key={idx} className="bg-turquoise-aqua/10 text-turquoise-aqua px-3 py-1 rounded-full text-sm font-medium">
                              {activity}
                            </span>
                          ))}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {activeTab === 'includes' && (
                <div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                    <div>
                      <h3 className="text-2xl font-bold text-deep-navy mb-6 flex items-center">
                        <span className="text-green-500 mr-2">✅</span>
                        Inclusions
                      </h3>
                      <ul className="space-y-3">
                        {pkg.includes.map((item, index) => (
                          <li key={index} className="flex items-start">
                            <svg className="w-5 h-5 text-green-500 mr-3 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                            </svg>
                            <span className="text-gray-700">{item}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                    <div>
                      <h3 className="text-2xl font-bold text-deep-navy mb-6 flex items-center">
                        <span className="text-red-500 mr-2">❌</span>
                        Exclusions
                      </h3>
                      <ul className="space-y-3">
                        {pkg.excludes.map((item, index) => (
                          <li key={index} className="flex items-start">
                            <svg className="w-5 h-5 text-red-500 mr-3 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                            </svg>
                            <span className="text-gray-700">{item}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>
              )}

              {activeTab === 'gallery' && (
                <div>
                  <h3 className="text-2xl font-bold text-deep-navy mb-6">Photo Gallery</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {pkg.gallery.map((image, index) => (
                      <div key={index} className="relative group overflow-hidden rounded-xl">
                        <img 
                          src={image} 
                          alt={`Gallery ${index + 1}`}
                          className="w-full h-48 object-cover transition-transform duration-300 group-hover:scale-110"
                        />
                        <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-all duration-300"></div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {activeTab === 'highlights' && (
                <div>
                  <h3 className="text-2xl font-bold text-deep-navy mb-6">Package Highlights</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {pkg.highlights.map((highlight, index) => (
                      <div key={index} className="flex items-center p-4 bg-gradient-to-r from-golden-sand/10 to-sunset-orange/10 rounded-xl">
                        <div className="w-8 h-8 bg-gradient-to-r from-golden-sand to-sunset-orange rounded-full flex items-center justify-center text-white font-bold text-sm mr-4">
                          ⭐
                        </div>
                        <span className="text-gray-700 font-medium">{highlight}</span>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Booking Card */}
          <div className="lg:col-span-1">
            <div className="sticky top-24">
              <div className="bg-white rounded-2xl shadow-xl p-8 border border-light-gray">
                <div className="text-center mb-6">
                  <div className="flex items-center justify-center mb-2">
                    <span className="text-3xl font-bold text-ocean-blue">{pkg.price}</span>
                    <span className="text-lg text-gray-500 line-through ml-2">{pkg.originalPrice}</span>
                  </div>
                  <p className="text-sm text-gray-600">per person (twin sharing)</p>
                  <div className="inline-flex items-center bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-semibold mt-2">
                    Save ₹6,000
                  </div>
                </div>

                <div className="space-y-4 mb-6">
                  <div className="flex justify-between items-center py-2 border-b border-gray-100">
                    <span className="text-gray-600">Duration</span>
                    <span className="font-semibold text-deep-navy">{pkg.duration}</span>
                  </div>
                  <div className="flex justify-between items-center py-2 border-b border-gray-100">
                    <span className="text-gray-600">Destinations</span>
                    <span className="font-semibold text-deep-navy">3 Islands</span>
                  </div>
                  <div className="flex justify-between items-center py-2 border-b border-gray-100">
                    <span className="text-gray-600">Accommodation</span>
                    <span className="font-semibold text-deep-navy">4-Star Resort</span>
                  </div>
                  <div className="flex justify-between items-center py-2">
                    <span className="text-gray-600">Meals</span>
                    <span className="font-semibold text-deep-navy">Breakfast + Dinner</span>
                  </div>
                </div>

                <button 
                  onClick={() => setShowQueryForm(true)}
                  className="w-full bg-gradient-to-r from-sunset-orange to-golden-sand text-white py-4 px-6 rounded-xl font-bold text-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 mb-4"
                >
                  Book This Package
                </button>

                <div className="text-center">
                  <p className="text-sm text-gray-500 mb-2">Need help? Call us</p>
                  <a href="tel:+919876543210" className="text-ocean-blue font-semibold hover:underline">
                    +91 98765 43210
                  </a>
                </div>

                <div className="mt-6 p-4 bg-blue-50 rounded-xl">
                  <h4 className="font-semibold text-deep-navy mb-2">Why Book With Us?</h4>
                  <ul className="text-sm text-gray-600 space-y-1">
                    <li>✓ Best Price Guarantee</li>
                    <li>✓ 24/7 Customer Support</li>
                    <li>✓ Instant Confirmation</li>
                    <li>✓ Secure Payment</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Related Packages */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold text-deep-navy text-center mb-12">You Might Also Like</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {[
              { title: "Mesmerizing Honeymoon", price: "₹28,000", duration: "4N/5D", image: "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=400&q=80" },
              { title: "Dazzle Andaman", price: "₹28,000", duration: "5N/6D", image: "https://images.unsplash.com/photo-1559827260-dc66d52bef19?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=400&q=80" },
              { title: "Romantic Paradise", price: "₹35,000", duration: "6N/7D", image: "https://images.unsplash.com/photo-1578662996442-48f60103fc96?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=400&q=80" }
            ].map((relatedPkg, index) => (
              <Link key={index} to={`/package/${index + 2}`} className="group">
                <div className="bg-white rounded-2xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2">
                  <div className="relative h-48">
                    <img 
                      src={relatedPkg.image} 
                      alt={relatedPkg.title}
                      className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-110"
                    />
                    <div className="absolute top-4 left-4">
                      <span className="bg-golden-sand text-deep-navy text-sm font-bold px-3 py-1 rounded-full">
                        {relatedPkg.duration}
                      </span>
                    </div>
                  </div>
                  <div className="p-6">
                    <h3 className="text-xl font-bold text-deep-navy mb-2">{relatedPkg.title}</h3>
                    <div className="flex justify-between items-center">
                      <span className="text-2xl font-bold text-ocean-blue">{relatedPkg.price}</span>
                      <span className="text-ocean-blue font-semibold group-hover:underline">View Details →</span>
                    </div>
                  </div>
                </div>
              </Link>
            ))}
          </div>
        </div>
      </section>

      {/* Query Form Modal */}
      {showQueryForm && (
        <QueryForm 
          title="Plan Your Dream Trip"
          packageName={pkg.title}
          onClose={() => setShowQueryForm(false)}
        />
      )}
    </div>
  );
};

export default PackageDetail;