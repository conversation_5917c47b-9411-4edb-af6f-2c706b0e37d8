import React, { useState } from 'react';
import { Link } from 'react-router-dom';

const HeroSection = ({ onShowQueryForm }) => {
  const [formData, setFormData] = useState({
    name: '',
    phone: '',
    packageType: '',
    travelDate: ''
  });

  const handleInputChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    console.log('Lead form submitted:', formData);
    alert('Thank you! We will contact you soon.');
  };

  return (
    <section className="relative min-h-screen flex items-center overflow-hidden">
      {/* Background Video/Image */}
      <div className="absolute inset-0 z-0">
        <img 
          src="https://images.unsplash.com/photo-1559827260-dc66d52bef19?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2000&q=80"
          alt="Andaman Paradise"
          className="w-full h-full object-cover"
        />
        <div className="absolute inset-0 bg-gradient-to-r from-deep-navy/90 via-ocean-blue/70 to-turquoise-aqua/60"></div>
      </div>

      {/* Floating Elements */}
      <div className="absolute top-20 left-10 w-32 h-32 bg-golden-sand/20 rounded-full blur-3xl animate-pulse"></div>
      <div className="absolute bottom-32 right-20 w-40 h-40 bg-sunset-orange/20 rounded-full blur-3xl animate-pulse delay-1000"></div>
      <div className="absolute top-1/2 left-1/4 w-24 h-24 bg-turquoise-aqua/30 rounded-full blur-2xl animate-pulse delay-500"></div>

      <div className="relative z-10 container mx-auto px-4 py-20">
        <div className="grid grid-cols-1 lg:grid-cols-12 gap-8 items-center">
          {/* Main Content */}
          <div className="lg:col-span-7 text-white">
            {/* Badge */}
            <div className="inline-flex items-center bg-golden-sand/20 backdrop-blur-sm rounded-full px-6 py-3 mb-8 border border-golden-sand/30">
              <div className="w-3 h-3 bg-golden-sand rounded-full mr-3 animate-pulse"></div>
              <span className="text-golden-sand font-semibold text-sm">
                #1 Rated Tour Operator in Andaman Islands
              </span>
            </div>

            {/* Main Heading */}
            <h1 className="text-5xl lg:text-7xl font-bold leading-tight mb-8">
              Discover
              <span className="block text-transparent bg-clip-text bg-gradient-to-r from-golden-sand via-sunset-orange to-turquoise-aqua">
                Paradise
              </span>
              in Andaman
            </h1>

            {/* Subtitle */}
            <p className="text-xl lg:text-2xl text-blue-100 leading-relaxed mb-10 max-w-2xl">
              Experience pristine beaches, crystal-clear waters, and vibrant marine life 
              with our expertly crafted tour packages designed for unforgettable memories.
            </p>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-6 mb-12">
              <button 
                onClick={onShowQueryForm}
                className="group bg-gradient-to-r from-sunset-orange to-golden-sand text-white px-8 py-4 rounded-full font-bold text-lg hover:shadow-2xl transition-all duration-300 transform hover:scale-105"
              >
                <span className="flex items-center justify-center">
                  Plan Your Dream Trip
                  <svg className="w-6 h-6 ml-3 group-hover:translate-x-2 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                  </svg>
                </span>
              </button>
              
              <Link 
                to="/tours"
                className="group border-2 border-white/30 backdrop-blur-sm text-white px-8 py-4 rounded-full font-bold text-lg hover:bg-white hover:text-deep-navy transition-all duration-300"
              >
                Explore Packages
              </Link>
            </div>

            {/* Stats */}
            <div className="grid grid-cols-3 gap-8">
              <div className="text-center">
                <div className="text-4xl font-bold text-golden-sand mb-2">1000+</div>
                <div className="text-blue-200 text-sm">Happy Travelers</div>
              </div>
              <div className="text-center">
                <div className="text-4xl font-bold text-golden-sand mb-2">50+</div>
                <div className="text-blue-200 text-sm">Tour Packages</div>
              </div>
              <div className="text-center">
                <div className="text-4xl font-bold text-golden-sand mb-2">4.9★</div>
                <div className="text-blue-200 text-sm">Customer Rating</div>
              </div>
            </div>
          </div>

          {/* Lead Form */}
          <div className="lg:col-span-5">
            <div className="bg-white/95 backdrop-blur-lg rounded-3xl p-8 shadow-2xl border border-white/20">
              <div className="text-center mb-6">
                <h3 className="text-2xl font-bold text-deep-navy mb-2">Get Best Deals</h3>
                <p className="text-gray-600">Fill the form and get instant quotes</p>
              </div>

              <form onSubmit={handleSubmit} className="space-y-4">
                <div>
                  <input
                    type="text"
                    name="name"
                    placeholder="Your Full Name"
                    value={formData.name}
                    onChange={handleInputChange}
                    className="w-full px-4 py-4 border border-light-gray rounded-xl focus:outline-none focus:ring-2 focus:ring-ocean-blue focus:border-transparent transition-all"
                    required
                  />
                </div>

                <div>
                  <input
                    type="tel"
                    name="phone"
                    placeholder="Phone Number"
                    value={formData.phone}
                    onChange={handleInputChange}
                    className="w-full px-4 py-4 border border-light-gray rounded-xl focus:outline-none focus:ring-2 focus:ring-ocean-blue focus:border-transparent transition-all"
                    required
                  />
                </div>

                <div>
                  <select
                    name="packageType"
                    value={formData.packageType}
                    onChange={handleInputChange}
                    className="w-full px-4 py-4 border border-light-gray rounded-xl focus:outline-none focus:ring-2 focus:ring-ocean-blue focus:border-transparent transition-all"
                    required
                  >
                    <option value="">Select Package Type</option>
                    <option value="honeymoon">Honeymoon Package</option>
                    <option value="family">Family Package</option>
                    <option value="group">Group Package</option>
                    <option value="adventure">Adventure Package</option>
                  </select>
                </div>

                <div>
                  <input
                    type="date"
                    name="travelDate"
                    placeholder="Travel Date"
                    value={formData.travelDate}
                    onChange={handleInputChange}
                    className="w-full px-4 py-4 border border-light-gray rounded-xl focus:outline-none focus:ring-2 focus:ring-ocean-blue focus:border-transparent transition-all"
                  />
                </div>

                <button
                  type="submit"
                  className="w-full bg-gradient-to-r from-sunset-orange to-golden-sand text-white py-4 px-6 rounded-xl font-bold text-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105"
                >
                  Get Instant Quote
                </button>
              </form>

              <div className="mt-6 text-center">
                <p className="text-sm text-gray-500">
                  🔒 Your information is safe and secure
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Scroll Indicator */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 text-white animate-bounce">
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
        </svg>
      </div>
    </section>
  );
};

export default HeroSection;