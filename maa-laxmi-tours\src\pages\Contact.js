import React, { useState } from 'react';
import SEO from '../components/SEO';

const Contact = () => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    subject: '',
    message: ''
  });

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    console.log('Contact form submitted:', formData);
    alert('Thank you for your message! We will get back to you soon.');
    setFormData({
      name: '',
      email: '',
      phone: '',
      subject: '',
      message: ''
    });
  };

  const contactInfo = [
    {
      icon: "📍",
      title: "Visit Our Office",
      details: ["Maa Laxmi Tour & Travels", "Aberdeen Bazaar, Port Blair", "Andaman & Nicobar Islands - 744101"],
      color: "from-ocean-blue to-turquoise-aqua"
    },
    {
      icon: "📞",
      title: "Call Us",
      details: ["+91 98765 43210", "+91 87654 32109", "Landline: ************"],
      color: "from-sunset-orange to-golden-sand"
    },
    {
      icon: "✉️",
      title: "Email Us",
      details: ["<EMAIL>", "<EMAIL>", "<EMAIL>"],
      color: "from-turquoise-aqua to-ocean-blue"
    },
    {
      icon: "🕒",
      title: "Business Hours",
      details: ["Mon - Sat: 9:00 AM - 7:00 PM", "Sunday: 10:00 AM - 5:00 PM", "Emergency Support: 24/7"],
      color: "from-golden-sand to-sunset-orange"
    }
  ];

  const socialLinks = [
    { name: "Facebook", icon: "📘", url: "#", color: "hover:text-blue-600" },
    { name: "Instagram", icon: "📷", url: "#", color: "hover:text-pink-600" },
    { name: "Twitter", icon: "🐦", url: "#", color: "hover:text-blue-400" },
    { name: "WhatsApp", icon: "💬", url: "#", color: "hover:text-green-600" },
    { name: "YouTube", icon: "📺", url: "#", color: "hover:text-red-600" }
  ];

  return (
    <div className="min-h-screen bg-soft-white">
      <SEO 
        title="Contact Maa Laxmi Tour & Travels - Get in Touch | Andaman Tour Experts"
        description="Contact Maa Laxmi Tour & Travels for Andaman tour packages, ferry bookings, and travel assistance. Call +91 98765 43210 or visit our Port Blair office."
        keywords="contact Maa Laxmi Tours, Andaman travel agent contact, Port Blair tour operator, Andaman tour booking contact"
      />

      {/* Hero Section */}
      <section className="relative py-20 bg-gradient-to-r from-ocean-blue via-turquoise-aqua to-deep-navy text-white overflow-hidden">
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="absolute top-10 left-10 w-32 h-32 bg-golden-sand/20 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-10 right-10 w-40 h-40 bg-sunset-orange/20 rounded-full blur-3xl animate-pulse delay-1000"></div>
        
        <div className="relative container mx-auto px-4 text-center">
          <div className="inline-flex items-center bg-white/20 backdrop-blur-sm rounded-full px-6 py-3 mb-6">
            <span className="text-white font-semibold">📞 Get In Touch</span>
          </div>
          <h1 className="text-5xl lg:text-6xl font-bold mb-6">
            Contact 
            <span className="text-golden-sand"> Our Experts</span>
          </h1>
          <p className="text-xl max-w-3xl mx-auto mb-8">
            Ready to plan your dream Andaman vacation? Our travel experts are here to help you create unforgettable memories
          </p>
          
          {/* Quick Contact */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a 
              href="tel:+919876543210"
              className="bg-gradient-to-r from-sunset-orange to-golden-sand text-white px-8 py-4 rounded-full font-bold text-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105"
            >
              📞 Call Now: +91 98765 43210
            </a>
            <a 
              href="https://wa.me/919876543210"
              className="border-2 border-white text-white px-8 py-4 rounded-full font-bold text-lg hover:bg-white hover:text-ocean-blue transition-all duration-300"
            >
              💬 WhatsApp Us
            </a>
          </div>
        </div>
      </section>

      {/* Contact Information Cards */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <div className="inline-flex items-center bg-ocean-blue/10 rounded-full px-4 py-2 mb-4">
              <span className="text-ocean-blue font-semibold text-sm">Contact Information</span>
            </div>
            <h2 className="text-4xl lg:text-5xl font-bold text-deep-navy mb-6">
              Multiple Ways to 
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-ocean-blue to-turquoise-aqua"> Reach Us</span>
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Choose the most convenient way to get in touch with our travel experts
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {contactInfo.map((info, index) => (
              <div key={index} className="group bg-white rounded-3xl shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2 overflow-hidden">
                <div className={`h-2 bg-gradient-to-r ${info.color}`}></div>
                <div className="p-8 text-center">
                  <div className="text-6xl mb-6">{info.icon}</div>
                  <h3 className="text-xl font-bold text-deep-navy mb-4">{info.title}</h3>
                  <div className="space-y-2">
                    {info.details.map((detail, idx) => (
                      <p key={idx} className="text-gray-600 text-sm leading-relaxed">
                        {detail}
                      </p>
                    ))}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Contact Form & Map Section */}
      <section className="py-20 bg-gradient-to-br from-gray-50 to-blue-50">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-start">
            {/* Contact Form */}
            <div>
              <div className="bg-white rounded-3xl shadow-2xl p-8 lg:p-12">
                <div className="text-center mb-8">
                  <h3 className="text-3xl font-bold text-deep-navy mb-4">Send Us a Message</h3>
                  <p className="text-gray-600">Fill out the form below and we'll get back to you within 24 hours</p>
                </div>

                <form onSubmit={handleSubmit} className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-semibold text-deep-navy mb-2">
                        Full Name *
                      </label>
                      <input
                        type="text"
                        name="name"
                        value={formData.name}
                        onChange={handleChange}
                        required
                        className="w-full px-4 py-4 border border-light-gray rounded-xl focus:outline-none focus:ring-2 focus:ring-ocean-blue focus:border-transparent transition-all"
                        placeholder="Enter your full name"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-semibold text-deep-navy mb-2">
                        Phone Number *
                      </label>
                      <input
                        type="tel"
                        name="phone"
                        value={formData.phone}
                        onChange={handleChange}
                        required
                        className="w-full px-4 py-4 border border-light-gray rounded-xl focus:outline-none focus:ring-2 focus:ring-ocean-blue focus:border-transparent transition-all"
                        placeholder="Enter your phone number"
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-semibold text-deep-navy mb-2">
                      Email Address *
                    </label>
                    <input
                      type="email"
                      name="email"
                      value={formData.email}
                      onChange={handleChange}
                      required
                      className="w-full px-4 py-4 border border-light-gray rounded-xl focus:outline-none focus:ring-2 focus:ring-ocean-blue focus:border-transparent transition-all"
                      placeholder="Enter your email address"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-semibold text-deep-navy mb-2">
                      Subject *
                    </label>
                    <select
                      name="subject"
                      value={formData.subject}
                      onChange={handleChange}
                      required
                      className="w-full px-4 py-4 border border-light-gray rounded-xl focus:outline-none focus:ring-2 focus:ring-ocean-blue focus:border-transparent transition-all"
                    >
                      <option value="">Select a subject</option>
                      <option value="tour-inquiry">Tour Package Inquiry</option>
                      <option value="booking">Booking Assistance</option>
                      <option value="ferry">Ferry Booking</option>
                      <option value="activities">Activity Booking</option>
                      <option value="complaint">Complaint/Feedback</option>
                      <option value="other">Other</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-semibold text-deep-navy mb-2">
                      Message *
                    </label>
                    <textarea
                      name="message"
                      value={formData.message}
                      onChange={handleChange}
                      required
                      rows="6"
                      className="w-full px-4 py-4 border border-light-gray rounded-xl focus:outline-none focus:ring-2 focus:ring-ocean-blue focus:border-transparent transition-all resize-none"
                      placeholder="Tell us about your travel plans or any questions you have..."
                    ></textarea>
                  </div>

                  <button
                    type="submit"
                    className="w-full bg-gradient-to-r from-sunset-orange to-golden-sand text-white py-4 px-6 rounded-xl font-bold text-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105"
                  >
                    Send Message
                  </button>
                </form>

                <div className="mt-8 text-center">
                  <p className="text-sm text-gray-500 mb-4">
                    🔒 Your information is safe and secure with us
                  </p>
                  <div className="flex justify-center space-x-4">
                    {socialLinks.map((social, index) => (
                      <a
                        key={index}
                        href={social.url}
                        className={`text-2xl ${social.color} transition-colors duration-300`}
                        title={social.name}
                      >
                        {social.icon}
                      </a>
                    ))}
                  </div>
                </div>
              </div>
            </div>

            {/* Office Info & Map */}
            <div className="space-y-8">
              {/* Office Details */}
              <div className="bg-white rounded-3xl shadow-2xl p-8">
                <h3 className="text-2xl font-bold text-deep-navy mb-6">Visit Our Office</h3>
                <div className="space-y-6">
                  <div className="flex items-start space-x-4">
                    <div className="w-12 h-12 bg-gradient-to-r from-ocean-blue to-turquoise-aqua rounded-xl flex items-center justify-center text-white text-xl">
                      📍
                    </div>
                    <div>
                      <h4 className="font-semibold text-deep-navy mb-1">Address</h4>
                      <p className="text-gray-600 leading-relaxed">
                        Maa Laxmi Tour & Travels<br />
                        Aberdeen Bazaar, Port Blair<br />
                        Andaman & Nicobar Islands - 744101<br />
                        India
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start space-x-4">
                    <div className="w-12 h-12 bg-gradient-to-r from-sunset-orange to-golden-sand rounded-xl flex items-center justify-center text-white text-xl">
                      🚗
                    </div>
                    <div>
                      <h4 className="font-semibold text-deep-navy mb-1">How to Reach</h4>
                      <p className="text-gray-600 leading-relaxed">
                        • 5 minutes from Veer Savarkar Airport<br />
                        • Near Aberdeen Clock Tower<br />
                        • Free parking available<br />
                        • Accessible by local transport
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start space-x-4">
                    <div className="w-12 h-12 bg-gradient-to-r from-turquoise-aqua to-ocean-blue rounded-xl flex items-center justify-center text-white text-xl">
                      ⭐
                    </div>
                    <div>
                      <h4 className="font-semibold text-deep-navy mb-1">Why Visit Us?</h4>
                      <p className="text-gray-600 leading-relaxed">
                        • Face-to-face consultation<br />
                        • View brochures and photos<br />
                        • Instant booking confirmation<br />
                        • Local expertise and tips
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Map Placeholder */}
              <div className="bg-white rounded-3xl shadow-2xl overflow-hidden">
                <div className="h-80 bg-gradient-to-br from-ocean-blue/20 to-turquoise-aqua/20 flex items-center justify-center">
                  <div className="text-center">
                    <div className="text-6xl mb-4">🗺️</div>
                    <h4 className="text-xl font-bold text-deep-navy mb-2">Interactive Map</h4>
                    <p className="text-gray-600">Google Maps integration will be added here</p>
                    <button className="mt-4 bg-gradient-to-r from-ocean-blue to-turquoise-aqua text-white px-6 py-2 rounded-full font-semibold hover:shadow-lg transition-all">
                      Get Directions
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-20 bg-soft-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <div className="inline-flex items-center bg-sunset-orange/10 rounded-full px-4 py-2 mb-4">
              <span className="text-sunset-orange font-semibold text-sm">Frequently Asked Questions</span>
            </div>
            <h2 className="text-4xl font-bold text-deep-navy mb-6">Quick Answers</h2>
            <p className="text-lg text-gray-600">Common questions about our services and Andaman travel</p>
          </div>

          <div className="max-w-4xl mx-auto">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {[
                {
                  q: "What's the best time to visit Andaman?",
                  a: "October to May is ideal with pleasant weather and calm seas perfect for water activities."
                },
                {
                  q: "Do I need permits for Andaman?",
                  a: "Indian citizens get permits on arrival. Foreign nationals need advance permission from authorities."
                },
                {
                  q: "How do I book ferry tickets?",
                  a: "We handle all ferry bookings as part of our packages or you can book separately through us."
                },
                {
                  q: "What's included in tour packages?",
                  a: "Accommodation, meals, transfers, sightseeing, and activities as per the package details."
                },
                {
                  q: "Is travel insurance recommended?",
                  a: "Yes, we highly recommend travel insurance for medical emergencies and trip cancellations."
                },
                {
                  q: "Can packages be customized?",
                  a: "Absolutely! We specialize in creating personalized itineraries based on your preferences."
                }
              ].map((faq, index) => (
                <div key={index} className="bg-white p-6 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300">
                  <h4 className="font-bold text-deep-navy mb-3">{faq.q}</h4>
                  <p className="text-gray-600">{faq.a}</p>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-ocean-blue via-turquoise-aqua to-deep-navy text-white relative overflow-hidden">
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="absolute top-10 left-10 w-32 h-32 bg-golden-sand/20 rounded-full blur-3xl"></div>
        <div className="absolute bottom-10 right-10 w-40 h-40 bg-sunset-orange/20 rounded-full blur-3xl"></div>
        
        <div className="relative container mx-auto px-4 text-center">
          <h2 className="text-4xl lg:text-5xl font-bold mb-6">
            Ready to Start Your 
            <span className="text-golden-sand"> Adventure?</span>
          </h2>
          <p className="text-xl mb-10 max-w-2xl mx-auto">
            Don't wait! Contact us today and let our experts plan your perfect Andaman getaway
          </p>
          
          <div className="flex flex-col sm:flex-row gap-6 justify-center">
            <a 
              href="tel:+919876543210"
              className="bg-gradient-to-r from-sunset-orange to-golden-sand text-white px-10 py-4 rounded-full font-bold text-lg hover:shadow-2xl transition-all duration-300 transform hover:scale-105"
            >
              Call Now: +91 98765 43210
            </a>
            <a 
              href="mailto:<EMAIL>"
              className="border-2 border-white text-white px-10 py-4 rounded-full font-bold text-lg hover:bg-white hover:text-ocean-blue transition-all duration-300"
            >
              Email Us
            </a>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Contact;