import React, {
  useState
} from 'react';
import {
  Link
} from 'react-router-dom';
import SEO from '../components/SEO';
import QueryForm from '../components/QueryForm';

const About = () => {
  const [showQueryForm, setShowQueryForm] = useState(false);

  const milestones = [{
      year: "2010",
      title: "Founded",
      description: "Started as a small local business with a passion for Andaman tourism",
      icon: "🚀"
    },
    {
      year: "2015",
      title: "Expansion",
      description: "Expanded services to include luxury packages and corporate tours",
      icon: "📈"
    },
    {
      year: "2018",
      title: "Recognition",
      description: "Awarded 'Best Tour Operator' by Andaman Tourism Board",
      icon: "🏆"
    },
    {
      year: "2024",
      title: "Excellence",
      description: "Serving 1000+ happy customers with 4.9★ rating",
      icon: "⭐"
    }
  ];

  const values = [{
      icon: "🌊",
      title: "Sustainability",
      description: "Committed to eco-friendly tourism that preserves the natural beauty of Andaman",
      color: "from-turquoise-aqua to-ocean-blue"
    },
    {
      icon: "🤝",
      title: "Integrity",
      description: "Transparent pricing, honest communication, and reliable service delivery",
      color: "from-sunset-orange to-golden-sand"
    },
    {
      icon: "💎",
      title: "Excellence",
      description: "Continuously improving our services to exceed customer expectations",
      color: "from-golden-sand to-sunset-orange"
    },
    {
      icon: "❤️",
      title: "Passion",
      description: "Genuine love for travel and creating unforgettable experiences",
      color: "from-ocean-blue to-turquoise-aqua"
    }
  ];

  const team = [{
      name: "Rajesh Kumar",
      position: "Founder & CEO",
      experience: "15+ Years Experience",
      image: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=300&q=80",
      description: "Visionary leader with deep expertise in Andaman tourism and hospitality industry",
      specialties: ["Tourism Strategy", "Business Development", "Customer Relations"]
    },
    {
      name: "Priya Sharma",
      position: "Operations Manager",
      experience: "12+ Years Experience",
      image: "https://images.unsplash.com/photo-1494790108755-2616b612b786?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=300&q=80",
      description: "Expert in tour planning and operations with a keen eye for detail and quality",
      specialties: ["Tour Planning", "Quality Control", "Team Management"]
    },
    {
      name: "Amit Singh",
      position: "Senior Tour Guide",
      experience: "10+ Years Experience",
      image: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=300&q=80",
      description: "Local expert and marine life enthusiast who brings islands to life through stories",
      specialties: ["Marine Biology", "Local History", "Adventure Sports"]
    },
    {
      name: "Sunita Devi",
      position: "Customer Relations",
      experience: "8+ Years Experience",
      image: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=300&q=80",
      description: "Dedicated to ensuring every customer has a seamless and memorable experience",
      specialties: ["Customer Support", "Travel Coordination", "Problem Resolution"]
    }
  ];

  const achievements = [{
      number: "1000+",
      label: "Happy Customers",
      description: "Satisfied travelers from across India and abroad",
      icon: "😊"
    },
    {
      number: "50+",
      label: "Tour Packages",
      description: "Diverse range of customized travel experiences",
      icon: "🏝️"
    },
    {
      number: "4.9★",
      label: "Customer Rating",
      description: "Consistently high ratings across all platforms",
      icon: "⭐"
    },
    {
      number: "24/7",
      label: "Support Available",
      description: "Round-the-clock assistance for all travelers",
      icon: "🕐"
    },
    {
      number: "15+",
      label: "Years Experience",
      description: "Over a decade of expertise in Andaman tourism",
      icon: "📅"
    },
    {
      number: "100%",
      label: "Safety Record",
      description: "Impeccable safety standards and protocols",
      icon: "🛡️"
    }
  ];

  const certifications = [{
      title: "Tourism Board Certified",
      description: "Officially recognized by Andaman & Nicobar Tourism Board",
      icon: "🏛️"
    },
    {
      title: "ISO 9001:2015",
      description: "Quality management system certification",
      icon: "📋"
    },
    {
      title: "IATA Approved",
      description: "International Air Transport Association member",
      icon: "✈️"
    },
    {
      title: "Eco Tourism Certified",
      description: "Committed to sustainable and responsible tourism",
      icon: "🌱"
    }
  ];

  return ( <
    div className = "min-h-screen bg-soft-white" >
    <
    SEO title = "About Maa Laxmi Tour & Travels - Your Trusted Andaman Travel Partner"
    description = "Learn about Maa Laxmi Tour & Travels - 15+ years of experience in Andaman tourism, 1000+ happy customers, and award-winning service. Your trusted travel partner."
    keywords = "about Maa Laxmi Tours, Andaman tour operator, travel company Andaman, tourism experience, travel expertise" /
    >

    {
      /* Hero Section */ } <
    section className = "relative py-32 bg-gradient-to-br from-ocean-blue via-turquoise-aqua to-ocean-blue text-white overflow-hidden" >
    <
    div className = "absolute inset-0 bg-black/20" > < /div> <
    div className = "absolute top-20 left-20 w-40 h-40 bg-golden-sand/20 rounded-full blur-3xl" > < /div> <
    div className = "absolute bottom-20 right-20 w-32 h-32 bg-sunset-orange/20 rounded-full blur-3xl" > < /div>

    <
    div className = "relative container mx-auto px-4 text-center" >
    <
    div className = "inline-flex items-center bg-white/10 backdrop-blur-sm rounded-full px-6 py-3 mb-8" >
    <
    span className = "text-white font-semibold" > About Our Company < /span> <
    /div> <
    h1 className = "text-5xl lg:text-7xl font-bold mb-8 leading-tight" >
    Your Trusted Partner
    for <
    span className = "block text-golden-sand" > Andaman Adventures < /span> <
    /h1> <
    p className = "text-xl lg:text-2xl mb-12 max-w-4xl mx-auto leading-relaxed" >
    With over 15 years of experience and 1000 + happy customers, we are the leading tour operator specializing in creating unforgettable Andaman experiences <
    /p> <
    div className = "flex flex-col sm:flex-row gap-6 justify-center" >
    <
    button onClick = {
      () => setShowQueryForm(true)
    }
    className = "bg-gradient-to-r from-sunset-orange to-golden-sand text-white px-10 py-4 rounded-full font-bold text-lg hover:shadow-2xl transition-all duration-300 transform hover:scale-105" >
    Plan Your Trip <
    /button> <
    Link to = "/contact"
    className = "border-2 border-white text-white px-10 py-4 rounded-full font-bold text-lg hover:bg-white hover:text-ocean-blue transition-all duration-300" >
    Contact Us <
    /Link> <
    /div> <
    /div> <
    /section>

    {
      /* Our Story Section */ } <
    section className = "py-20 bg-gradient-to-br from-soft-white to-blue-50" >
    <
    div className = "container mx-auto px-4" >
    <
    div className = "grid grid-cols-1 lg:grid-cols-2 gap-16 items-center" >
    <
    div className = "space-y-8" >
    <
    div >
    <
    div className = "inline-flex items-center bg-ocean-blue/10 rounded-full px-4 py-2 mb-4" >
    <
    span className = "text-ocean-blue font-semibold text-sm" > Our Story < /span> <
    /div> <
    h2 className = "text-4xl lg:text-5xl font-bold text-deep-navy mb-6" >
    A Journey That Started with <
    span className = "text-transparent bg-clip-text bg-gradient-to-r from-ocean-blue to-turquoise-aqua" > Passion < /span> <
    /h2> <
    p className = "text-lg text-gray-600 leading-relaxed mb-6" >
    Founded in 2010 with a simple dream - to share the breathtaking beauty of Andaman Islands with the world.What started as a small local business has grown into one of the most trusted tour operators in the region. <
    /p> <
    p className = "text-lg text-gray-600 leading-relaxed mb-6" >
    Our founder, Rajesh Kumar, fell in love with these pristine islands during his first visit and decided to make it his mission to help others experience the same magic.Today, we are proud to have served over 1000 satisfied customers and
    continue to set new standards in
      Andaman tourism. <
      /p> <
      p className = "text-lg text-gray-600 leading-relaxed" >
      Every tour we organize, every experience we curate, and every smile we create is a testament
    to our unwavering commitment to excellence and our deep love
    for these beautiful islands. <
    /p> <
    /div> <
    /div>

    <
    div className = "relative" >
    <
    div className = "absolute -top-4 -right-4 w-72 h-72 bg-gradient-to-r from-sunset-orange/20 to-golden-sand/20 rounded-full blur-3xl" > < /div> <
    img src = "https://images.unsplash.com/photo-1544551763-46a013bb70d5?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80"
    alt = "Our Story"
    className = "relative rounded-3xl shadow-2xl" /
    >
    <
    div className = "absolute -bottom-6 -left-6 bg-white p-6 rounded-2xl shadow-xl" >
    <
    div className = "flex items-center space-x-4" >
    <
    div className = "w-12 h-12 bg-gradient-to-r from-ocean-blue to-turquoise-aqua rounded-xl flex items-center justify-center" >
    <
    span className = "text-white font-bold text-xl" > 15 + < /span> <
    /div> <
    div >
    <
    div className = "font-bold text-deep-navy" > Years of Excellence < /div> <
    div className = "text-sm text-gray-500" > Serving since 2010 < /div> <
    /div> <
    /div> <
    /div> <
    /div> <
    /div> <
    /div> <
    /section>

    {
      /* Mission & Vision */ } <
    section className = "py-20 bg-soft-white" >
    <
    div className = "container mx-auto px-4" >
    <
    div className = "text-center mb-16" >
    <
    div className = "inline-flex items-center bg-sunset-orange/10 rounded-full px-4 py-2 mb-4" >
    <
    span className = "text-sunset-orange font-semibold text-sm" > Mission & Vision < /span> <
    /div> <
    h2 className = "text-4xl lg:text-5xl font-bold text-deep-navy mb-6" >
    What Drives Us <
    span className = "text-transparent bg-clip-text bg-gradient-to-r from-sunset-orange to-golden-sand" > Forward < /span> <
    /h2> <
    /div>

    <
    div className = "grid grid-cols-1 lg:grid-cols-2 gap-12" >
    <
    div className = "group bg-white p-10 rounded-3xl shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2" >
    <
    div className = "w-20 h-20 bg-gradient-to-r from-ocean-blue to-turquoise-aqua rounded-2xl flex items-center justify-center mb-8 group-hover:scale-110 transition-transform duration-300" >
    <
    svg className = "w-10 h-10 text-white"
    fill = "none"
    stroke = "currentColor"
    viewBox = "0 0 24 24" >
    <
    path strokeLinecap = "round"
    strokeLinejoin = "round"
    strokeWidth = {
      2
    }
    d = "M13 10V3L4 14h7v7l9-11h-7z" / >
    <
    /svg> <
    /div> <
    h3 className = "text-3xl font-bold text-deep-navy mb-6" > Our Mission < /h3> <
    p className = "text-lg text-gray-600 leading-relaxed" >
    To provide exceptional travel experiences that create lasting memories
    while promoting sustainable tourism and supporting local communities in Andaman & Nicobar Islands.We strive to be the bridge between travelers and the authentic beauty of these pristine islands. <
    /p> <
    /div>

    <
    div className = "group bg-white p-10 rounded-3xl shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2" >
    <
    div className = "w-20 h-20 bg-gradient-to-r from-sunset-orange to-golden-sand rounded-2xl flex items-center justify-center mb-8 group-hover:scale-110 transition-transform duration-300" >
    <
    svg className = "w-10 h-10 text-white"
    fill = "none"
    stroke = "currentColor"
    viewBox = "0 0 24 24" >
    <
    path strokeLinecap = "round"
    strokeLinejoin = "round"
    strokeWidth = {
      2
    }
    d = "M15 12a3 3 0 11-6 0 3 3 0 016 0z" / >
    <
    path strokeLinecap = "round"
    strokeLinejoin = "round"
    strokeWidth = {
      2
    }
    d = "M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" / >
    <
    /svg> <
    /div> <
    h3 className = "text-3xl font-bold text-deep-navy mb-6" > Our Vision < /h3> <
    p className = "text-lg text-gray-600 leading-relaxed" >
    To be the most trusted and preferred travel partner
    for Andaman tourism, known
    for our integrity, innovation, and commitment to delivering world - class travel experiences.We envision a future where every traveler leaves with a piece of Andaman in their heart. <
    /p> <
    /div> <
    /div> <
    /div> <
    /section>

    {
      /* Our Values */ } <
    section className = "py-20 bg-gradient-to-br from-gray-50 to-blue-50" >
    <
    div className = "container mx-auto px-4" >
    <
    div className = "text-center mb-16" >
    <
    div className = "inline-flex items-center bg-turquoise-aqua/10 rounded-full px-4 py-2 mb-4" >
    <
    span className = "text-turquoise-aqua font-semibold text-sm" > Our Values < /span> <
    /div> <
    h2 className = "text-4xl lg:text-5xl font-bold text-deep-navy mb-6" >
    Values That <
    span className = "text-transparent bg-clip-text bg-gradient-to-r from-turquoise-aqua to-ocean-blue" > Guide Us < /span> <
    /h2> <
    p className = "text-lg text-gray-600 max-w-2xl mx-auto" >
    These core values shape every decision we make and every service we provide <
    /p> <
    /div>

    <
    div className = "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8" > {
      values.map((value, index) => ( <
        div key = {
          index
        }
        className = "group text-center p-8 bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2" >
        <
        div className = {
          `w-20 h-20 bg-gradient-to-r ${value.color} rounded-2xl flex items-center justify-center mx-auto mb-6 text-3xl group-hover:scale-110 transition-transform duration-300`
        } > {
          value.icon
        } <
        /div> <
        h3 className = "text-xl font-bold text-deep-navy mb-4" > {
          value.title
        } < /h3> <
        p className = "text-gray-600" > {
          value.description
        } < /p> <
        /div>
      ))
    } <
    /div> <
    /div> <
    /section>

    {
      /* Journey Timeline */ } <
    section className = "py-20 bg-soft-white" >
    <
    div className = "container mx-auto px-4" >
    <
    div className = "text-center mb-16" >
    <
    div className = "inline-flex items-center bg-golden-sand/10 rounded-full px-4 py-2 mb-4" >
    <
    span className = "text-golden-sand font-semibold text-sm" > Our Journey < /span> <
    /div> <
    h2 className = "text-4xl lg:text-5xl font-bold text-deep-navy mb-6" >
    Milestones of
    <
    span className = "text-transparent bg-clip-text bg-gradient-to-r from-golden-sand to-sunset-orange" > Excellence < /span> <
    /h2> <
    /div>

    <
    div className = "relative" > {
      /* Timeline Line */ } <
    div className = "absolute left-1/2 transform -translate-x-1/2 w-1 h-full bg-gradient-to-b from-ocean-blue to-turquoise-aqua rounded-full" > < /div>

    <
    div className = "space-y-16" > {
      milestones.map((milestone, index) => ( <
        div key = {
          index
        }
        className = {
          `flex items-center ${index % 2 === 0 ? 'flex-row' : 'flex-row-reverse'}`
        } >
        <
        div className = {
          `w-1/2 ${index % 2 === 0 ? 'pr-12 text-right' : 'pl-12 text-left'}`
        } >
        <
        div className = "bg-white p-8 rounded-2xl shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2" >
        <
        div className = "text-6xl mb-4" > {
          milestone.icon
        } < /div> <
        div className = "text-2xl font-bold text-ocean-blue mb-2" > {
          milestone.year
        } < /div> <
        h3 className = "text-xl font-bold text-deep-navy mb-4" > {
          milestone.title
        } < /h3> <
        p className = "text-gray-600" > {
          milestone.description
        } < /p> <
        /div> <
        /div>

        {
          /* Timeline Dot */ } <
        div className = "relative z-10 w-6 h-6 bg-gradient-to-r from-sunset-orange to-golden-sand rounded-full border-4 border-white shadow-lg" > < /div>

        <
        div className = "w-1/2" > < /div> <
        /div>
      ))
    } <
    /div> <
    /div> <
    /div> <
    /section>

    {
      /* Team Section */ } <
    section className = "py-20 bg-gradient-to-br from-gray-50 to-blue-50" >
    <
    div className = "container mx-auto px-4" >
    <
    div className = "text-center mb-16" >
    <
    div className = "inline-flex items-center bg-ocean-blue/10 rounded-full px-4 py-2 mb-4" >
    <
    span className = "text-ocean-blue font-semibold text-sm" > Our Team < /span> <
    /div> <
    h2 className = "text-4xl lg:text-5xl font-bold text-deep-navy mb-6" >
    Meet the People Behind <
    span className = "text-transparent bg-clip-text bg-gradient-to-r from-ocean-blue to-turquoise-aqua" > Your Perfect Vacation < /span> <
    /h2> <
    p className = "text-lg text-gray-600 max-w-2xl mx-auto" >
    Our passionate team of travel experts is dedicated to making your Andaman experience unforgettable <
    /p> <
    /div>

    <
    div className = "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8" > {
      team.map((member, index) => ( <
        div key = {
          index
        }
        className = "group bg-white rounded-3xl shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 overflow-hidden" >
        <
        div className = "relative h-64 overflow-hidden" >
        <
        img src = {
          member.image
        }
        alt = {
          member.name
        }
        className = "w-full h-full object-cover transition-transform duration-300 group-hover:scale-110" /
        >
        <
        div className = "absolute inset-0 bg-gradient-to-t from-black/50 to-transparent" > < /div> <
        div className = "absolute bottom-4 left-4 text-white" >
        <
        div className = "text-xs bg-golden-sand text-deep-navy px-2 py-1 rounded-full font-semibold mb-2" > {
          member.experience
        } <
        /div> <
        /div> <
        /div>

        <
        div className = "p-6" >
        <
        h3 className = "text-xl font-bold text-deep-navy mb-2" > {
          member.name
        } < /h3> <
        p className = "text-ocean-blue font-semibold mb-3" > {
          member.position
        } < /p> <
        p className = "text-gray-600 text-sm mb-4" > {
          member.description
        } < /p>

        <
        div className = "space-y-2" > {
          member.specialties.map((specialty, idx) => ( <
            span key = {
              idx
            }
            className = "inline-block bg-turquoise-aqua/10 text-turquoise-aqua text-xs px-3 py-1 rounded-full mr-2" > {
              specialty
            } <
            /span>
          ))
        } <
        /div> <
        /div> <
        /div>
      ))
    } <
    /div> <
    /div> <
    /section>

    {
      /* Achievements Section */ } <
    section className = "py-20 bg-gradient-to-r from-ocean-blue via-turquoise-aqua to-ocean-blue text-white relative overflow-hidden" >
    <
    div className = "absolute inset-0 bg-black/20" > < /div> <
    div className = "absolute top-10 left-10 w-32 h-32 bg-golden-sand/20 rounded-full blur-3xl" > < /div> <
    div className = "absolute bottom-10 right-10 w-40 h-40 bg-sunset-orange/20 rounded-full blur-3xl" > < /div>

    <
    div className = "relative container mx-auto px-4" >
    <
    div className = "text-center mb-16" >
    <
    div className = "inline-flex items-center bg-white/10 backdrop-blur-sm rounded-full px-4 py-2 mb-4" >
    <
    span className = "text-white font-semibold text-sm" > Our Achievements < /span> <
    /div> <
    h2 className = "text-4xl lg:text-5xl font-bold mb-6" >
    Numbers That Speak
    for <
    span className = "text-golden-sand" > Our Excellence < /span> <
    /h2> <
    /div>

    <
    div className = "grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-8" > {
      achievements.map((achievement, index) => ( <
        div key = {
          index
        }
        className = "text-center group" >
        <
        div className = "text-4xl mb-4 group-hover:scale-110 transition-transform duration-300" > {
          achievement.icon
        } <
        /div> <
        div className = "text-3xl lg:text-4xl font-bold mb-2 text-golden-sand" > {
          achievement.number
        } <
        /div> <
        div className = "text-lg font-semibold mb-2" > {
          achievement.label
        } < /div> <
        div className = "text-sm text-white/80" > {
          achievement.description
        } < /div> <
        /div>
      ))
    } <
    /div> <
    /div> <
    /section>

    {
      /* Certifications */ } <
    section className = "py-20 bg-soft-white" >
    <
    div className = "container mx-auto px-4" >
    <
    div className = "text-center mb-16" >
    <
    div className = "inline-flex items-center bg-sunset-orange/10 rounded-full px-4 py-2 mb-4" >
    <
    span className = "text-sunset-orange font-semibold text-sm" > Certifications < /span> <
    /div> <
    h2 className = "text-4xl lg:text-5xl font-bold text-deep-navy mb-6" >
    Trusted &
    <
    span className = "text-transparent bg-clip-text bg-gradient-to-r from-sunset-orange to-golden-sand" > Certified < /span> <
    /h2> <
    p className = "text-lg text-gray-600 max-w-2xl mx-auto" >
    Our certifications and recognitions reflect our commitment to quality and excellence <
    /p> <
    /div>

    <
    div className = "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8" > {
      certifications.map((cert, index) => ( <
        div key = {
          index
        }
        className = "group text-center p-8 bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2" >
        <
        div className = "text-5xl mb-6 group-hover:scale-110 transition-transform duration-300" > {
          cert.icon
        } <
        /div> <
        h3 className = "text-xl font-bold text-deep-navy mb-4" > {
          cert.title
        } < /h3> <
        p className = "text-gray-600" > {
          cert.description
        } < /p> <
        /div>
      ))
    } <
    /div> <
    /div> <
    /section>

    {
      /* CTA Section */ } <
    section className = "py-20 bg-gradient-to-br from-gray-50 to-blue-50" >
    <
    div className = "container mx-auto px-4 text-center" >
    <
    div className = "max-w-4xl mx-auto" >
    <
    h2 className = "text-4xl lg:text-5xl font-bold text-deep-navy mb-6" >
    Ready to Experience the <
    span className = "text-transparent bg-clip-text bg-gradient-to-r from-ocean-blue to-turquoise-aqua" > Andaman Magic ? < /span> <
    /h2> <
    p className = "text-xl text-gray-600 mb-10" >
    Let our experienced team create the perfect Andaman adventure tailored just
    for you.Your dream vacation is just one click away!
    <
    /p>

    <
    div className = "flex flex-col sm:flex-row gap-6 justify-center" >
    <
    button onClick = {
      () => setShowQueryForm(true)
    }
    className = "bg-gradient-to-r from-sunset-orange to-golden-sand text-white px-10 py-4 rounded-full font-bold text-lg hover:shadow-2xl transition-all duration-300 transform hover:scale-105" >
    Start Planning Now <
    /button> <
    Link to = "/tours"
    className = "border-2 border-ocean-blue text-ocean-blue px-10 py-4 rounded-full font-bold text-lg hover:bg-ocean-blue hover:text-white transition-all duration-300" >
    View Tour Packages <
    /Link> <
    /div> <
    /div> <
    /div> <
    /section>

    {
      /* Query Form Modal */ } {
      showQueryForm && ( <
        QueryForm title = "Plan Your Andaman Adventure"
        onClose = {
          () => setShowQueryForm(false)
        }
        />
      )
    } <
    /div>
  );
};

export default About;