/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./src/**/*.{js,jsx,ts,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        // Primary Colors
        'ocean-blue': '#0077B6',
        'sunset-orange': '#FF8500',
        // Secondary Colors
        'turquoise-aqua': '#00B4D8',
        'golden-sand': '#FFD166',
        // Neutrals
        'soft-white': '#F9F9F9',
        'deep-navy': '#023047',
        'light-gray': '#E5E5E5',
        // Legacy support
        primary: {
          50: '#f0f9ff',
          100: '#e0f2fe',
          500: '#0077B6',
          600: '#005f8a',
          700: '#023047',
        },
        secondary: {
          500: '#FF8500',
          600: '#e6770a',
        }
      },
      fontFamily: {
        sans: ['Inter', 'system-ui', 'sans-serif'],
      }
    },
  },
  plugins: [
    require('@tailwindcss/forms'),
    require('@tailwindcss/typography'),
  ],
}

