import React, { useState, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isToursOpen, setIsToursOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const location = useLocation();

  const isActive = (path) => location.pathname === path || location.pathname.startsWith(path);

  // Handle scroll effect
  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 20);
    };
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Close mobile menu when route changes
  useEffect(() => {
    setIsMenuOpen(false);
    setIsToursOpen(false);
  }, [location]);

  // Handle body scroll lock when mobile menu is open
  useEffect(() => {
    if (isMenuOpen) {
      document.body.classList.add('menu-open');
    } else {
      document.body.classList.remove('menu-open');
    }
    
    // Cleanup on unmount
    return () => {
      document.body.classList.remove('menu-open');
    };
  }, [isMenuOpen]);

  const tourSubPages = [
    { name: 'Honeymoon', path: '/tours/honeymoon', icon: '💕' },
    { name: 'Family', path: '/tours/family', icon: '👨‍👩‍👧‍👦' },
    { name: 'Group', path: '/tours/group', icon: '👥' },
    { name: 'Adventure', path: '/tours/adventure', icon: '🏄‍♂️' }
  ];

  const navLinks = [
    { name: 'Home', path: '/', icon: '🏠' },
    { name: 'About', path: '/about', icon: 'ℹ️' },
    { name: 'Ferry', path: '/ferry', icon: '⛴️' },
    { name: 'Activities', path: '/activities', icon: '🏄‍♂️' },
    { name: 'Blog', path: '/blog', icon: '📝' },
    { name: 'Contact', path: '/contact', icon: '📞' }
  ];

  return (
    <>
      <header className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
        isScrolled 
          ? 'bg-white/95 backdrop-blur-lg shadow-lg border-b border-light-gray/20' 
          : 'bg-white/90 backdrop-blur-sm'
      }`}>
        <div className="container mx-auto px-4">
          <div className="flex justify-between items-center py-4">
            {/* Logo */}
            <Link to="/" className="flex items-center space-x-3 group">
              <div className="relative">
                <div className="w-12 h-12 bg-gradient-to-r from-ocean-blue to-turquoise-aqua rounded-2xl flex items-center justify-center transform group-hover:scale-110 transition-transform duration-300">
                  <span className="text-white font-bold text-xl">M</span>
                </div>
                <div className="absolute -top-1 -right-1 w-4 h-4 bg-sunset-orange rounded-full animate-pulse"></div>
              </div>
              <div>
                <h1 className="text-xl font-bold text-deep-navy group-hover:text-ocean-blue transition-colors">
                  Maa Laxmi
                </h1>
                <p className="text-sm text-gray-500 font-medium">Tour & Travels</p>
              </div>
            </Link>

            {/* Desktop Navigation */}
            <nav className="hidden lg:flex items-center space-x-1">
              <Link 
                to="/" 
                className={`px-4 py-2 rounded-full font-medium transition-all duration-300 ${
                  isActive('/') && location.pathname === '/'
                    ? 'bg-ocean-blue text-white shadow-lg' 
                    : 'text-deep-navy hover:bg-ocean-blue/10 hover:text-ocean-blue'
                }`}
              >
                Home
              </Link>
              
              <Link 
                to="/about" 
                className={`px-4 py-2 rounded-full font-medium transition-all duration-300 ${
                  isActive('/about') 
                    ? 'bg-ocean-blue text-white shadow-lg' 
                    : 'text-deep-navy hover:bg-ocean-blue/10 hover:text-ocean-blue'
                }`}
              >
                About
              </Link>
              
              {/* Tours Dropdown */}
              <div className="relative">
                <button 
                  className={`px-4 py-2 rounded-full font-medium transition-all duration-300 flex items-center space-x-2 ${
                    isActive('/tours') 
                      ? 'bg-ocean-blue text-white shadow-lg' 
                      : 'text-deep-navy hover:bg-ocean-blue/10 hover:text-ocean-blue'
                  }`}
                  onMouseEnter={() => setIsToursOpen(true)}
                  onMouseLeave={() => setIsToursOpen(false)}
                >
                  <span>Tours</span>
                  <svg 
                    className={`w-4 h-4 transition-transform duration-300 ${isToursOpen ? 'rotate-180' : ''}`} 
                    fill="none" 
                    stroke="currentColor" 
                    viewBox="0 0 24 24"
                  >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                </button>
                
                {/* Dropdown Menu */}
                <div 
                  className={`absolute top-full left-0 mt-2 w-64 bg-white/95 backdrop-blur-lg rounded-2xl shadow-2xl border border-light-gray/20 py-2 transition-all duration-300 ${
                    isToursOpen ? 'opacity-100 visible translate-y-0' : 'opacity-0 invisible -translate-y-2'
                  }`}
                  onMouseEnter={() => setIsToursOpen(true)}
                  onMouseLeave={() => setIsToursOpen(false)}
                >
                  {tourSubPages.map((item, index) => (
                    <Link 
                      key={index}
                      to={item.path} 
                      className="flex items-center space-x-3 px-4 py-3 text-deep-navy hover:bg-gradient-to-r hover:from-ocean-blue/10 hover:to-turquoise-aqua/10 hover:text-ocean-blue transition-all duration-300 group"
                    >
                      <span className="text-lg group-hover:scale-110 transition-transform duration-300">
                        {item.icon}
                      </span>
                      <span className="font-medium">{item.name}</span>
                      <svg className="w-4 h-4 ml-auto opacity-0 group-hover:opacity-100 transition-opacity" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                      </svg>
                    </Link>
                  ))}
                </div>
              </div>

              {navLinks.slice(2).map((link, index) => (
                <Link 
                  key={index}
                  to={link.path} 
                  className={`px-4 py-2 rounded-full font-medium transition-all duration-300 ${
                    isActive(link.path) 
                      ? 'bg-ocean-blue text-white shadow-lg' 
                      : 'text-deep-navy hover:bg-ocean-blue/10 hover:text-ocean-blue'
                  }`}
                >
                  {link.name}
                </Link>
              ))}
            </nav>

            {/* CTA Button & Mobile Menu Button */}
            <div className="flex items-center space-x-4">
              {/* CTA Button */}
              <Link 
                to="/contact" 
                className="hidden lg:flex items-center space-x-2 bg-gradient-to-r from-sunset-orange to-golden-sand text-white px-6 py-3 rounded-full font-semibold hover:shadow-xl transition-all duration-300 transform hover:scale-105"
              >
                <span>Book Now</span>
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                </svg>
              </Link>

              {/* Mobile Menu Button */}
              <button 
                className="lg:hidden relative w-10 h-10 bg-gradient-to-r from-ocean-blue to-turquoise-aqua rounded-xl flex items-center justify-center text-white hover:shadow-lg transition-all duration-300 transform hover:scale-105"
                onClick={() => setIsMenuOpen(!isMenuOpen)}
              >
                <div className="relative w-5 h-5">
                  <span className={`absolute block w-5 h-0.5 bg-white transform transition-all duration-300 ${
                    isMenuOpen ? 'rotate-45 translate-y-0' : '-translate-y-1.5'
                  }`}></span>
                  <span className={`absolute block w-5 h-0.5 bg-white transform transition-all duration-300 ${
                    isMenuOpen ? 'opacity-0' : 'opacity-100'
                  }`}></span>
                  <span className={`absolute block w-5 h-0.5 bg-white transform transition-all duration-300 ${
                    isMenuOpen ? '-rotate-45 translate-y-0' : 'translate-y-1.5'
                  }`}></span>
                </div>
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Mobile Menu Overlay */}
      <div className={`fixed inset-0 z-40 lg:hidden transition-all duration-300 ${
        isMenuOpen ? 'visible' : 'invisible'
      }`}>
        {/* Backdrop */}
        <div 
          className={`absolute inset-0 bg-black/50 backdrop-blur-sm transition-opacity duration-300 ${
            isMenuOpen ? 'opacity-100' : 'opacity-0'
          }`}
          onClick={() => setIsMenuOpen(false)}
        ></div>

        {/* Mobile Menu Panel */}
        <div className={`absolute top-0 right-0 h-full w-80 max-w-[85vw] bg-white/95 backdrop-blur-lg shadow-2xl transform transition-transform duration-300 ${
          isMenuOpen ? 'translate-x-0' : 'translate-x-full'
        }`}>
          {/* Menu Header */}
          <div className="flex items-center justify-between p-6 border-b border-light-gray/20">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-r from-ocean-blue to-turquoise-aqua rounded-xl flex items-center justify-center">
                <span className="text-white font-bold">M</span>
              </div>
              <div>
                <h2 className="font-bold text-deep-navy">Maa Laxmi</h2>
                <p className="text-xs text-gray-500">Tour & Travels</p>
              </div>
            </div>
            <button 
              onClick={() => setIsMenuOpen(false)}
              className="w-8 h-8 bg-gray-100 rounded-lg flex items-center justify-center text-gray-500 hover:bg-gray-200 transition-colors"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          {/* Menu Content */}
          <div className="p-6 space-y-2 overflow-y-auto h-full pb-32">
            {/* Home */}
            <Link 
              to="/" 
              className={`flex items-center space-x-4 p-4 rounded-2xl transition-all duration-300 ${
                isActive('/') && location.pathname === '/' 
                  ? 'bg-gradient-to-r from-ocean-blue to-turquoise-aqua text-white shadow-lg' 
                  : 'text-deep-navy hover:bg-ocean-blue/10'
              }`}
            >
              <span className="text-xl">🏠</span>
              <span className="font-medium">Home</span>
            </Link>

            {/* About */}
            <Link 
              to="/about" 
              className={`flex items-center space-x-4 p-4 rounded-2xl transition-all duration-300 ${
                isActive('/about') 
                  ? 'bg-gradient-to-r from-ocean-blue to-turquoise-aqua text-white shadow-lg' 
                  : 'text-deep-navy hover:bg-ocean-blue/10'
              }`}
            >
              <span className="text-xl">ℹ️</span>
              <span className="font-medium">About</span>
            </Link>

            {/* Tours with Expandable Submenu */}
            <div className="space-y-2">
              <button 
                onClick={() => setIsToursOpen(!isToursOpen)}
                className={`w-full flex items-center justify-between p-4 rounded-2xl transition-all duration-300 ${
                  isActive('/tours') 
                    ? 'bg-gradient-to-r from-ocean-blue to-turquoise-aqua text-white shadow-lg' 
                    : 'text-deep-navy hover:bg-ocean-blue/10'
                }`}
              >
                <div className="flex items-center space-x-4">
                  <span className="text-xl">🏝️</span>
                  <span className="font-medium">Tours</span>
                </div>
                <div className={`w-8 h-8 rounded-full flex items-center justify-center transition-all duration-300 ${
                  isToursOpen ? 'bg-white/20 rotate-45' : 'bg-white/10'
                }`}>
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                  </svg>
                </div>
              </button>

              {/* Tours Submenu */}
              <div className={`ml-4 space-y-1 overflow-hidden transition-all duration-300 ${
                isToursOpen ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'
              }`}>
                {tourSubPages.map((item, index) => (
                  <Link 
                    key={index}
                    to={item.path} 
                    className={`flex items-center space-x-3 p-3 rounded-xl transition-all duration-300 ${
                      isActive(item.path) 
                        ? 'bg-gradient-to-r from-sunset-orange/20 to-golden-sand/20 text-sunset-orange border border-sunset-orange/20' 
                        : 'text-gray-600 hover:bg-gray-100 hover:text-deep-navy'
                    }`}
                  >
                    <span className="text-lg">{item.icon}</span>
                    <span className="font-medium">{item.name}</span>
                  </Link>
                ))}
              </div>
            </div>

            {/* Other Navigation Links */}
            {navLinks.slice(2).map((link, index) => (
              <Link 
                key={index}
                to={link.path} 
                className={`flex items-center space-x-4 p-4 rounded-2xl transition-all duration-300 ${
                  isActive(link.path) 
                    ? 'bg-gradient-to-r from-ocean-blue to-turquoise-aqua text-white shadow-lg' 
                    : 'text-deep-navy hover:bg-ocean-blue/10'
                }`}
              >
                <span className="text-xl">{link.icon}</span>
                <span className="font-medium">{link.name}</span>
              </Link>
            ))}

            {/* CTA Button in Mobile */}
            <div className="pt-6">
              <Link 
                to="/contact" 
                className="flex items-center justify-center space-x-2 w-full bg-gradient-to-r from-sunset-orange to-golden-sand text-white p-4 rounded-2xl font-semibold shadow-lg hover:shadow-xl transition-all duration-300"
              >
                <span>Book Your Trip Now</span>
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                </svg>
              </Link>
            </div>

            {/* Contact Info in Mobile */}
            <div className="pt-6 border-t border-light-gray/20">
              <div className="space-y-3">
                <div className="flex items-center space-x-3 text-gray-600">
                  <div className="w-8 h-8 bg-turquoise-aqua/20 rounded-lg flex items-center justify-center">
                    <svg className="w-4 h-4 text-turquoise-aqua" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                    </svg>
                  </div>
                  <span className="text-sm font-medium">+91 98765 43210</span>
                </div>
                <div className="flex items-center space-x-3 text-gray-600">
                  <div className="w-8 h-8 bg-sunset-orange/20 rounded-lg flex items-center justify-center">
                    <svg className="w-4 h-4 text-sunset-orange" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                    </svg>
                  </div>
                  <span className="text-sm font-medium"><EMAIL></span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Spacer to prevent content from hiding behind fixed header */}
      <div className="h-20"></div>
    </>
  );
};

export default Header;