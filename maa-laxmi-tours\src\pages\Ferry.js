import React, { useState } from 'react';
import QueryForm from '../components/QueryForm';
import SEO from '../components/SEO';

const Ferry = () => {
  const [showQueryForm, setShowQueryForm] = useState(false);
  const [selectedFerry, setSelectedFerry] = useState('');

  const ferryServices = [
    {
      id: 1,
      name: "<PERSON>kru<PERSON>",
      route: "Port Blair ↔ Havelock",
      duration: "1.5 hours",
      price: "₹1,354",
      image: "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80",
      schedule: [
        { departure: "06:30 AM", arrival: "08:00 AM", status: "Available" },
        { departure: "08:15 AM", arrival: "09:45 AM", status: "Available" },
        { departure: "11:30 AM", arrival: "01:00 PM", status: "Filling Fast" },
        { departure: "02:15 PM", arrival: "03:45 PM", status: "Available" }
      ],
      features: ["Air Conditioned", "Comfortable Seating", "Life Jackets", "Refreshments"],
      type: "Premium"
    },
    {
      id: 2,
      name: "Green Ocean",
      route: "Port Blair ↔ Havelock",
      duration: "2 hours",
      price: "₹1,287",
      image: "https://images.unsplash.com/photo-1559827260-dc66d52bef19?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80",
      schedule: [
        { departure: "07:00 AM", arrival: "09:00 AM", status: "Available" },
        { departure: "09:30 AM", arrival: "11:30 AM", status: "Available" },
        { departure: "12:00 PM", arrival: "02:00 PM", status: "Available" },
        { departure: "03:30 PM", arrival: "05:30 PM", status: "Sold Out" }
      ],
      features: ["Economy Class", "Basic Seating", "Life Jackets", "Snacks Available"],
      type: "Economy"
    },
    {
      id: 3,
      name: "Coastal Cruise",
      route: "Port Blair ↔ Neil Island",
      duration: "1 hour",
      price: "₹1,200",
      image: "https://images.unsplash.com/photo-1578662996442-48f60103fc96?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80",
      schedule: [
        { departure: "08:00 AM", arrival: "09:00 AM", status: "Available" },
        { departure: "10:30 AM", arrival: "11:30 AM", status: "Available" },
        { departure: "01:00 PM", arrival: "02:00 PM", status: "Available" },
        { departure: "04:00 PM", arrival: "05:00 PM", status: "Available" }
      ],
      features: ["Premium Service", "Comfortable Seating", "Life Jackets", "Complimentary Water"],
      type: "Premium"
    },
    {
      id: 4,
      name: "Sea Link",
      route: "Havelock ↔ Neil Island",
      duration: "45 minutes",
      price: "₹950",
      image: "https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80",
      schedule: [
        { departure: "09:15 AM", arrival: "10:00 AM", status: "Available" },
        { departure: "11:45 AM", arrival: "12:30 PM", status: "Available" },
        { departure: "02:30 PM", arrival: "03:15 PM", status: "Filling Fast" },
        { departure: "05:00 PM", arrival: "05:45 PM", status: "Available" }
      ],
      features: ["Inter-Island Service", "Quick Transit", "Life Jackets", "Basic Amenities"],
      type: "Standard"
    }
  ];

  const handleFerryClick = (ferryName) => {
    setSelectedFerry(ferryName);
    setShowQueryForm(true);
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'Available': return 'text-green-600 bg-green-100';
      case 'Filling Fast': return 'text-orange-600 bg-orange-100';
      case 'Sold Out': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getTypeColor = (type) => {
    switch (type) {
      case 'Premium': return 'from-golden-sand to-sunset-orange';
      case 'Economy': return 'from-turquoise-aqua to-ocean-blue';
      case 'Standard': return 'from-ocean-blue to-deep-navy';
      default: return 'from-gray-400 to-gray-600';
    }
  };

  return (
    <div className="min-h-screen bg-soft-white">
      <SEO 
        title="Ferry Services Andaman - Island Hopping Made Easy | Maa Laxmi Tours"
        description="Book ferry tickets for Andaman islands. Reliable ferry services connecting Port Blair, Havelock, and Neil Island with comfortable seating and safety."
        keywords="Andaman ferry booking, Havelock ferry, Neil Island ferry, Port Blair ferry services, island hopping Andaman"
      />

      {/* Hero Section */}
      <section className="relative py-20 bg-gradient-to-r from-ocean-blue via-turquoise-aqua to-deep-navy text-white overflow-hidden">
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="absolute top-10 left-10 w-32 h-32 bg-golden-sand/20 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-10 right-10 w-40 h-40 bg-sunset-orange/20 rounded-full blur-3xl animate-pulse delay-1000"></div>
        
        <div className="relative container mx-auto px-4 text-center">
          <div className="inline-flex items-center bg-white/20 backdrop-blur-sm rounded-full px-6 py-3 mb-6">
            <span className="text-white font-semibold">⛴️ Ferry Services</span>
          </div>
          <h1 className="text-5xl lg:text-6xl font-bold mb-6">
            Island 
            <span className="text-golden-sand"> Hopping</span> Made Easy
          </h1>
          <p className="text-xl max-w-3xl mx-auto mb-8">
            Comfortable and reliable ferry services connecting all major islands in Andaman with modern amenities and safety
          </p>
          
          {/* Quick Stats */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-2xl mx-auto">
            <div className="text-center">
              <div className="text-3xl font-bold text-golden-sand">6+</div>
              <div className="text-sm opacity-90">Ferry Routes</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-golden-sand">20+</div>
              <div className="text-sm opacity-90">Daily Trips</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-golden-sand">100%</div>
              <div className="text-sm opacity-90">Safety Record</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-golden-sand">24/7</div>
              <div className="text-sm opacity-90">Support</div>
            </div>
          </div>
        </div>
      </section>

      {/* Ferry Services Grid */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <div className="inline-flex items-center bg-ocean-blue/10 rounded-full px-4 py-2 mb-4">
              <span className="text-ocean-blue font-semibold text-sm">Available Services</span>
            </div>
            <h2 className="text-4xl lg:text-5xl font-bold text-deep-navy mb-6">
              Choose Your 
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-ocean-blue to-turquoise-aqua"> Ferry Service</span>
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Modern ferries with comfortable seating, safety equipment, and professional crew
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {ferryServices.map(ferry => (
              <div key={ferry.id} className="bg-white rounded-3xl shadow-xl overflow-hidden hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2">
                {/* Ferry Image Header */}
                <div className="relative h-48 overflow-hidden">
                  <img 
                    src={ferry.image} 
                    alt={ferry.name}
                    className="w-full h-full object-cover transition-transform duration-700 hover:scale-110"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent"></div>
                  
                  {/* Type Badge */}
                  <div className="absolute top-4 left-4">
                    <span className={`bg-gradient-to-r ${getTypeColor(ferry.type)} text-white text-sm font-bold px-4 py-2 rounded-full shadow-lg`}>
                      {ferry.type}
                    </span>
                  </div>

                  {/* Duration Badge */}
                  <div className="absolute top-4 right-4">
                    <span className="bg-white/20 backdrop-blur-sm text-white text-sm font-semibold px-3 py-1 rounded-full">
                      {ferry.duration}
                    </span>
                  </div>

                  {/* Ferry Info */}
                  <div className="absolute bottom-4 left-4 text-white">
                    <h3 className="text-2xl font-bold mb-1">{ferry.name}</h3>
                    <p className="text-sm opacity-90">{ferry.route}</p>
                  </div>

                  {/* Price */}
                  <div className="absolute bottom-4 right-4 text-white text-right">
                    <div className="text-2xl font-bold">{ferry.price}</div>
                    <div className="text-xs opacity-80">per person</div>
                  </div>
                </div>

                {/* Ferry Details */}
                <div className="p-6">
                  {/* Schedule */}
                  <div className="mb-6">
                    <h4 className="font-bold text-deep-navy mb-4 flex items-center">
                      <svg className="w-5 h-5 mr-2 text-ocean-blue" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      Today's Schedule
                    </h4>
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                      {ferry.schedule.map((time, index) => (
                        <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-xl hover:bg-gray-100 transition-colors">
                          <div className="flex items-center">
                            <div className="text-sm">
                              <div className="font-semibold text-ocean-blue">{time.departure}</div>
                              <div className="text-gray-500 text-xs">Departure</div>
                            </div>
                            <svg className="w-4 h-4 mx-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                            </svg>
                            <div className="text-sm">
                              <div className="font-semibold text-turquoise-aqua">{time.arrival}</div>
                              <div className="text-gray-500 text-xs">Arrival</div>
                            </div>
                          </div>
                          <span className={`text-xs font-semibold px-2 py-1 rounded-full ${getStatusColor(time.status)}`}>
                            {time.status}
                          </span>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Features */}
                  <div className="mb-6">
                    <h4 className="font-bold text-deep-navy mb-3 flex items-center">
                      <svg className="w-5 h-5 mr-2 text-ocean-blue" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                      Features & Amenities
                    </h4>
                    <div className="grid grid-cols-2 gap-2">
                      {ferry.features.map((feature, index) => (
                        <div key={index} className="flex items-center text-sm text-gray-600">
                          <div className="w-2 h-2 bg-turquoise-aqua rounded-full mr-2"></div>
                          {feature}
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Book Button */}
                  <button 
                    onClick={() => handleFerryClick(ferry.name)}
                    className="w-full bg-gradient-to-r from-sunset-orange to-golden-sand text-white py-4 px-6 rounded-xl font-bold text-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105"
                  >
                    Book Ferry Tickets
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Important Information */}
      <section className="py-20 bg-gradient-to-br from-gray-50 to-blue-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-16">
              <div className="inline-flex items-center bg-sunset-orange/10 rounded-full px-4 py-2 mb-4">
                <span className="text-sunset-orange font-semibold text-sm">Important Information</span>
              </div>
              <h2 className="text-4xl font-bold text-deep-navy mb-6">Travel Guidelines & Safety</h2>
              <p className="text-lg text-gray-600">Everything you need to know for a smooth ferry journey</p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              <div className="bg-white p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300">
                <div className="w-16 h-16 bg-gradient-to-r from-ocean-blue to-turquoise-aqua rounded-2xl flex items-center justify-center mb-6 text-white text-2xl">
                  📋
                </div>
                <h3 className="text-xl font-bold text-deep-navy mb-4">Booking Guidelines</h3>
                <ul className="space-y-3 text-gray-600">
                  <li className="flex items-start">
                    <svg className="w-5 h-5 text-ocean-blue mr-2 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    Book tickets at least 24 hours in advance
                  </li>
                  <li className="flex items-start">
                    <svg className="w-5 h-5 text-ocean-blue mr-2 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    Carry valid photo ID for all passengers
                  </li>
                  <li className="flex items-start">
                    <svg className="w-5 h-5 text-ocean-blue mr-2 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    Arrive at jetty 30 minutes before departure
                  </li>
                  <li className="flex items-start">
                    <svg className="w-5 h-5 text-ocean-blue mr-2 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    Children below 2 years travel free
                  </li>
                </ul>
              </div>

              <div className="bg-white p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300">
                <div className="w-16 h-16 bg-gradient-to-r from-sunset-orange to-golden-sand rounded-2xl flex items-center justify-center mb-6 text-white text-2xl">
                  🛡️
                </div>
                <h3 className="text-xl font-bold text-deep-navy mb-4">Safety & Comfort</h3>
                <ul className="space-y-3 text-gray-600">
                  <li className="flex items-start">
                    <svg className="w-5 h-5 text-sunset-orange mr-2 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    Life jackets provided for all passengers
                  </li>
                  <li className="flex items-start">
                    <svg className="w-5 h-5 text-sunset-orange mr-2 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    Experienced crew and safety equipment
                  </li>
                  <li className="flex items-start">
                    <svg className="w-5 h-5 text-sunset-orange mr-2 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    Weather-dependent operations for safety
                  </li>
                  <li className="flex items-start">
                    <svg className="w-5 h-5 text-sunset-orange mr-2 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    First aid facilities available onboard
                  </li>
                </ul>
              </div>

              <div className="bg-white p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300">
                <div className="w-16 h-16 bg-gradient-to-r from-turquoise-aqua to-ocean-blue rounded-2xl flex items-center justify-center mb-6 text-white text-2xl">
                  💼
                </div>
                <h3 className="text-xl font-bold text-deep-navy mb-4">Luggage & Facilities</h3>
                <ul className="space-y-3 text-gray-600">
                  <li className="flex items-start">
                    <svg className="w-5 h-5 text-turquoise-aqua mr-2 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    20kg luggage allowance per person
                  </li>
                  <li className="flex items-start">
                    <svg className="w-5 h-5 text-turquoise-aqua mr-2 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    Onboard refreshments available
                  </li>
                  <li className="flex items-start">
                    <svg className="w-5 h-5 text-turquoise-aqua mr-2 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    Clean restroom facilities
                  </li>
                  <li className="flex items-start">
                    <svg className="w-5 h-5 text-turquoise-aqua mr-2 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    Air-conditioned seating areas
                  </li>
                </ul>
              </div>
            </div>

            <div className="mt-12 bg-gradient-to-r from-golden-sand/10 to-sunset-orange/10 border border-golden-sand/20 rounded-2xl p-8">
              <div className="flex items-start">
                <div className="flex-shrink-0">
                  <div className="w-12 h-12 bg-golden-sand rounded-full flex items-center justify-center text-white text-xl">
                    ⚠️
                  </div>
                </div>
                <div className="ml-6">
                  <h4 className="text-xl font-bold text-deep-navy mb-2">Weather Advisory</h4>
                  <p className="text-gray-600">
                    Ferry services may be suspended during rough weather conditions for passenger safety. 
                    We recommend checking weather conditions and confirming your booking before traveling. 
                    Full refund or rescheduling available in case of weather-related cancellations.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-ocean-blue via-turquoise-aqua to-deep-navy text-white relative overflow-hidden">
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="absolute top-10 left-10 w-32 h-32 bg-golden-sand/20 rounded-full blur-3xl"></div>
        <div className="absolute bottom-10 right-10 w-40 h-40 bg-sunset-orange/20 rounded-full blur-3xl"></div>
        
        <div className="relative container mx-auto px-4 text-center">
          <h2 className="text-4xl lg:text-5xl font-bold mb-6">
            Ready to Explore 
            <span className="text-golden-sand"> The Islands?</span>
          </h2>
          <p className="text-xl mb-10 max-w-2xl mx-auto">
            Book your ferry tickets now and start your island hopping adventure in Andaman
          </p>
          
          <div className="flex flex-col sm:flex-row gap-6 justify-center">
            <button 
              onClick={() => setShowQueryForm(true)}
              className="bg-gradient-to-r from-sunset-orange to-golden-sand text-white px-10 py-4 rounded-full font-bold text-lg hover:shadow-2xl transition-all duration-300 transform hover:scale-105"
            >
              Book Ferry Now
            </button>
            <a 
              href="tel:+919876543210"
              className="border-2 border-white text-white px-10 py-4 rounded-full font-bold text-lg hover:bg-white hover:text-ocean-blue transition-all duration-300"
            >
              Call for Booking
            </a>
          </div>
        </div>
      </section>

      {/* Query Form Modal */}
      {showQueryForm && (
        <QueryForm 
          title="Ferry Booking Inquiry"
          packageName={`Ferry Service - ${selectedFerry}`}
          onClose={() => setShowQueryForm(false)}
        />
      )}
    </div>
  );
};

export default Ferry;