# Email Setup Guide for Maa Laxmi Tours

This guide explains how to set up email functionality for your Maa Laxmi Tours website.

## 🚀 Quick Setup Options

### Option 1: Backend Server (Recommended for Production)

1. **Deploy the Backend Server**
   ```bash
   cd backend-example
   npm install
   cp .env.example .env
   # Edit .env with your email credentials
   npm start
   ```

2. **Environment Variables (.env file)**
   ```env
   PORT=3001
   EMAIL_USER=<EMAIL>
   EMAIL_PASS=your-app-password
   BUSINESS_EMAIL=<EMAIL>
   ```

3. **Gmail Setup**
   - Enable 2-factor authentication on your Gmail account
   - Generate an "App Password" (not your regular password)
   - Use the app password in EMAIL_PASS

4. **Deploy Backend**
   - Deploy to Heroku, Railway, or any Node.js hosting service
   - Update the API endpoint in your React app

### Option 2: EmailJS (Client-side, No Backend Required)

1. **Install EmailJS**
   ```bash
   npm install @emailjs/browser
   ```

2. **Setup EmailJS Account**
   - Go to https://www.emailjs.com/
   - Create account and get your credentials
   - Create email template

3. **Update QueryForm.js**
   ```javascript
   import emailjs from '@emailjs/browser';
   
   const handleSubmit = async (e) => {
     e.preventDefault();
     setIsSubmitting(true);
     
     try {
       await emailjs.send(
         'YOUR_SERVICE_ID',
         'YOUR_TEMPLATE_ID',
         {
           from_name: formData.name,
           from_email: formData.email,
           phone: formData.phone,
           message: formData.message,
           // ... other fields
         },
         'YOUR_PUBLIC_KEY'
       );
       
       alert('Thank you! We will contact you soon.');
       if (onClose) onClose();
     } catch (error) {
       alert('Error sending message. Please try again.');
     } finally {
       setIsSubmitting(false);
     }
   };
   ```

## 📧 Email Templates

The system sends two emails:

1. **Business Email** - Notification to your business email with customer details
2. **Customer Email** - Confirmation email to the customer

## 🔧 Production Deployment

### Frontend (React App)
- Deploy to Netlify, Vercel, or any static hosting
- Update API endpoints to your backend URL

### Backend (Node.js)
- Deploy to Heroku, Railway, DigitalOcean, or AWS
- Set environment variables in your hosting platform
- Ensure CORS is configured for your frontend domain

## 📱 Testing

1. **Local Testing**
   ```bash
   # Start backend
   cd backend-example
   npm run dev
   
   # Start frontend (in another terminal)
   cd ..
   npm start
   ```

2. **Test Email Sending**
   - Fill out the contact form
   - Check your email for notifications
   - Verify customer receives confirmation

## 🛡️ Security Best Practices

1. **Never commit .env files** - Add to .gitignore
2. **Use App Passwords** - Don't use your main email password
3. **Enable 2FA** - On your email account
4. **Validate Input** - Server-side validation for all form data
5. **Rate Limiting** - Prevent spam submissions

## 🔄 Alternative Email Services

### Using SendGrid
```javascript
const sgMail = require('@sendgrid/mail');
sgMail.setApiKey(process.env.SENDGRID_API_KEY);

const msg = {
  to: '<EMAIL>',
  from: '<EMAIL>',
  subject: 'New Query',
  html: emailHTML,
};

await sgMail.send(msg);
```

### Using AWS SES
```javascript
const AWS = require('aws-sdk');
const ses = new AWS.SES({ region: 'us-east-1' });

const params = {
  Source: '<EMAIL>',
  Destination: { ToAddresses: ['<EMAIL>'] },
  Message: {
    Subject: { Data: 'New Query' },
    Body: { Html: { Data: emailHTML } }
  }
};

await ses.sendEmail(params).promise();
```

## 📞 Support

If you need help setting up emails:
1. Check the console for error messages
2. Verify your email credentials
3. Test with a simple email first
4. Contact your hosting provider for server issues

## 🎯 Quick Checklist

- [ ] Backend server deployed and running
- [ ] Environment variables configured
- [ ] Email credentials working
- [ ] Frontend pointing to correct API endpoint
- [ ] Test email sending functionality
- [ ] Customer confirmation emails working
- [ ] Business notification emails working

---

**Note:** Replace all placeholder emails and credentials with your actual business information before deploying to production.