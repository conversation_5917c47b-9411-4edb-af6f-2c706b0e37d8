import React, {
  useState,
  useEffect
} from 'react';

const QueryForm = ({
  title = "Plan Your Dream Trip",
  onClose,
  packageName = ""
}) => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    adults: '2',
    children: '0',
    travelDate: '',
    duration: '',
    packageName: packageName,
    budget: '',
    message: ''
  });

  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    document.body.style.overflow = 'hidden';
    return () => {
      document.body.style.overflow = 'unset';
    };
  }, []);

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      const response = await fetch('/api/send-email', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          formData
        }),
      });

      if (response.ok) {
        alert('🎉 Thank you for your query! Our travel experts will contact you within 24 hours.');
        if (onClose) onClose();
      } else {
        throw new Error('Failed to send email');
      }
    } catch (error) {
      console.error('Error sending email:', error);
      alert('Thank you for your query! We will contact you soon. Call us at +91 98765 43210.');
      if (onClose) onClose();
    } finally {
      setIsSubmitting(false);
    }
  };

  return ( <
    div className = "fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50 p-4" >
    <
    div className = "bg-white rounded-3xl max-w-md w-full max-h-[90vh] overflow-hidden shadow-2xl" > {
      /* Header */
    } <
    div className = "relative bg-gradient-to-r from-ocean-blue to-turquoise-aqua text-white p-6" > {
      onClose && ( <
        button onClick = {
          onClose
        }
        className = "absolute top-4 right-4 w-8 h-8 bg-white/20 hover:bg-white/30 rounded-full flex items-center justify-center transition-colors" >
        <
        svg className = "w-5 h-5"
        fill = "none"
        stroke = "currentColor"
        viewBox = "0 0 24 24" >
        <
        path strokeLinecap = "round"
        strokeLinejoin = "round"
        strokeWidth = {
          2
        }
        d = "M6 18L18 6M6 6l12 12" / >
        <
        /svg> < /
        button >
      )
    }

    <
    h3 className = "text-2xl font-bold mb-2" > {
      title
    } < /h3> <
    p className = "text-white/90" > Let 's create your perfect Andaman experience</p> < /
    div >

    {
      /* Form Content */
    } <
    div className = "p-6 overflow-y-auto max-h-[60vh]" >
    <
    form onSubmit = {
      handleSubmit
    }
    className = "space-y-4" >
    <
    div >
    <
    label className = "block text-sm font-semibold text-deep-navy mb-2" >
    Full Name *
    <
    /label> <
    input type = "text"
    name = "name"
    value = {
      formData.name
    }
    onChange = {
      handleChange
    }
    required className = "w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:outline-none focus:border-ocean-blue transition-colors bg-gray-50 focus:bg-white"
    placeholder = "Enter your full name" /
    >
    <
    /div>

    <
    div >
    <
    label className = "block text-sm font-semibold text-deep-navy mb-2" >
    Email Address *
    <
    /label> <
    input type = "email"
    name = "email"
    value = {
      formData.email
    }
    onChange = {
      handleChange
    }
    required className = "w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:outline-none focus:border-ocean-blue transition-colors bg-gray-50 focus:bg-white"
    placeholder = "<EMAIL>" /
    >
    <
    /div>

    <
    div >
    <
    label className = "block text-sm font-semibold text-deep-navy mb-2" >
    Phone Number *
    <
    /label> <
    input type = "tel"
    name = "phone"
    value = {
      formData.phone
    }
    onChange = {
      handleChange
    }
    required className = "w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:outline-none focus:border-ocean-blue transition-colors bg-gray-50 focus:bg-white"
    placeholder = "+91 98765 43210" /
    >
    <
    /div>

    <
    div className = "grid grid-cols-2 gap-4" >
    <
    div >
    <
    label className = "block text-sm font-semibold text-deep-navy mb-2" >
    Adults *
    <
    /label> <
    select name = "adults"
    value = {
      formData.adults
    }
    onChange = {
      handleChange
    }
    className = "w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:outline-none focus:border-ocean-blue transition-colors bg-gray-50 focus:bg-white" > {
      [1, 2, 3, 4, 5, 6, 7, 8].map(num => ( <
        option key = {
          num
        }
        value = {
          num
        } > {
          num
        }
        Adult {
          num > 1 ? 's' : ''
        } < /option>
      ))
    } <
    /select> < /
    div > <
    div >
    <
    label className = "block text-sm font-semibold text-deep-navy mb-2" >
    Children <
    /label> <
    select name = "children"
    value = {
      formData.children
    }
    onChange = {
      handleChange
    }
    className = "w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:outline-none focus:border-ocean-blue transition-colors bg-gray-50 focus:bg-white" > {
      [0, 1, 2, 3, 4, 5, 6].map(num => ( <
        option key = {
          num
        }
        value = {
          num
        } > {
          num
        } {
          num === 1 ? 'Child' : 'Children'
        } < /option>
      ))
    } <
    /select> < /
    div > <
    /div>

    <
    div >
    <
    label className = "block text-sm font-semibold text-deep-navy mb-2" >
    Preferred Travel Date *
    <
    /label> <
    input type = "date"
    name = "travelDate"
    value = {
      formData.travelDate
    }
    onChange = {
      handleChange
    }
    required min = {
      new Date().toISOString().split('T')[0]
    }
    className = "w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:outline-none focus:border-ocean-blue transition-colors bg-gray-50 focus:bg-white" /
    >
    <
    /div>

    <
    div >
    <
    label className = "block text-sm font-semibold text-deep-navy mb-2" >
    Duration <
    /label> <
    select name = "duration"
    value = {
      formData.duration
    }
    onChange = {
      handleChange
    }
    className = "w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:outline-none focus:border-ocean-blue transition-colors bg-gray-50 focus:bg-white" >
    <
    option value = "" > Select Duration < /option> <
    option value = "3-4" > 3 - 4 Days < /option> <
    option value = "4-5" > 4 - 5 Days < /option> <
    option value = "5-6" > 5 - 6 Days < /option> <
    option value = "6-7" > 6 - 7 Days < /option> <
    option value = "7-10" > 7 - 10 Days < /option> <
    option value = "10+" > 10 + Days < /option> < /
    select > <
    /div>

    <
    div >
    <
    label className = "block text-sm font-semibold text-deep-navy mb-2" >
    Budget Range(Per Person) <
    /label> <
    select name = "budget"
    value = {
      formData.budget
    }
    onChange = {
      handleChange
    }
    className = "w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:outline-none focus:border-ocean-blue transition-colors bg-gray-50 focus:bg-white" >
    <
    option value = "" > Select Budget Range < /option> <
    option value = "15000-25000" > ₹15, 000 - ₹25, 000 < /option> <
    option value = "25000-35000" > ₹25, 000 - ₹35, 000 < /option> <
    option value = "35000-50000" > ₹35, 000 - ₹50, 000 < /option> <
    option value = "50000-75000" > ₹50, 000 - ₹75, 000 < /option> <
    option value = "75000+" > ₹75, 000 + < /option> < /
    select > <
    /div>

    {
      packageName && ( <
        div >
        <
        label className = "block text-sm font-semibold text-deep-navy mb-2" >
        Selected Package <
        /label> <
        input type = "text"
        name = "packageName"
        value = {
          formData.packageName
        }
        onChange = {
          handleChange
        }
        readOnly className = "w-full px-4 py-3 border-2 border-golden-sand/30 rounded-xl bg-golden-sand/10 text-deep-navy font-semibold" /
        >
        <
        /div>
      )
    }

    <
    div >
    <
    label className = "block text-sm font-semibold text-deep-navy mb-2" >
    Special Requirements or Message <
    /label> <
    textarea name = "message"
    value = {
      formData.message
    }
    onChange = {
      handleChange
    }
    rows = "3"
    className = "w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:outline-none focus:border-ocean-blue transition-colors bg-gray-50 focus:bg-white resize-none"
    placeholder = "Tell us about any special requirements..." >
    <
    /textarea> < /
    div >

    <
    div className = "bg-gradient-to-r from-ocean-blue/10 to-turquoise-aqua/10 p-4 rounded-xl" >
    <
    div className = "flex items-start space-x-3" >
    <
    svg className = "w-5 h-5 text-ocean-blue mt-0.5 flex-shrink-0"
    fill = "none"
    stroke = "currentColor"
    viewBox = "0 0 24 24" >
    <
    path strokeLinecap = "round"
    strokeLinejoin = "round"
    strokeWidth = {
      2
    }
    d = "M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" / >
    <
    /svg> <
    div className = "text-sm text-deep-navy" >
    <
    p className = "font-semibold mb-1" > What happens next ? < /p> <
    ul className = "space-y-1 text-gray-600" >
    <
    li > •Our travel expert will contact you within 24 hours < /li> <
    li > •We 'll create a customized itinerary for you</li> <
    li > •Get detailed pricing and package options < /li> <
    li > •Book with confidence - 100 % satisfaction guarantee < /li> < /
    ul > <
    /div> < /
    div > <
    /div>

    <
    button type = "submit"
    disabled = {
      isSubmitting
    }
    className = "w-full px-8 py-3 bg-gradient-to-r from-sunset-orange to-golden-sand text-white rounded-xl font-bold hover:shadow-xl transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2" > {
      isSubmitting ? ( <
        >
        <
        svg className = "animate-spin w-5 h-5"
        fill = "none"
        viewBox = "0 0 24 24" >
        <
        circle className = "opacity-25"
        cx = "12"
        cy = "12"
        r = "10"
        stroke = "currentColor"
        strokeWidth = "4" > < /circle> <
        path className = "opacity-75"
        fill = "currentColor"
        d = "M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" > < /path> < /
        svg > <
        span > Sending... < /span> < /
        >
      ) : ( <
        >
        <
        span > Send Query < /span> <
        svg className = "w-5 h-5"
        fill = "none"
        stroke = "currentColor"
        viewBox = "0 0 24 24" >
        <
        path strokeLinecap = "round"
        strokeLinejoin = "round"
        strokeWidth = {
          2
        }
        d = "M12 19l9 2-9-18-9 18 9-2zm0 0v-8" / >
        <
        /svg> < /
        >
      )
    } <
    /button> < /
    form > <
    /div> < /
    div > <
    /div>
  );
};

export default QueryForm;