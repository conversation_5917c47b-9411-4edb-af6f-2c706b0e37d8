// Email Service for Maa Laxmi Tours
// This service handles sending emails from the contact forms

const EMAIL_API_ENDPOINT = process.env.REACT_APP_EMAIL_API_URL || 'http://localhost:3001/api/send-email';

export const sendEmail = async (formData) => {
    try {
        const response = await fetch(EMAIL_API_ENDPOINT, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                to: '<EMAIL>', // Replace with your actual email
                subject: `New Query from ${formData.name} - <PERSON>a <PERSON>`,
                html: generateEmailHTML(formData),
                formData: formData
            }),
        });

        if (!response.ok) {
            throw new Error('Failed to send email');
        }

        return await response.json();
    } catch (error) {
        console.error('Email sending error:', error);
        throw error;
    }
};

const generateEmailHTML = (formData) => {
    return `
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <title>New Query - Maa Laxmi Tours</title>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: linear-gradient(135deg, #1e40af, #06b6d4); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
            .content { background: #f8fafc; padding: 30px; border-radius: 0 0 10px 10px; }
            .field { margin-bottom: 20px; }
            .label { font-weight: bold; color: #1e40af; display: block; margin-bottom: 5px; }
            .value { background: white; padding: 10px; border-radius: 5px; border-left: 4px solid #06b6d4; }
            .highlight { background: linear-gradient(135deg, #f59e0b, #f97316); color: white; padding: 15px; border-radius: 8px; margin: 20px 0; }
            .footer { text-align: center; margin-top: 30px; padding: 20px; color: #666; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>🏝️ New Customer Query</h1>
                <p>Maa Laxmi Tour & Travels</p>
            </div>
            
            <div class="content">
                <div class="highlight">
                    <strong>📧 New query received from ${formData.name}</strong><br>
                    <small>Received on: ${new Date().toLocaleString()}</small>
                </div>

                <div class="field">
                    <span class="label">👤 Customer Name:</span>
                    <div class="value">${formData.name}</div>
                </div>

                <div class="field">
                    <span class="label">📧 Email Address:</span>
                    <div class="value">${formData.email}</div>
                </div>

                <div class="field">
                    <span class="label">📱 Phone Number:</span>
                    <div class="value">${formData.phone}</div>
                </div>

                <div class="field">
                    <span class="label">👥 Travel Group:</span>
                    <div class="value">${formData.adults} Adult${formData.adults > 1 ? 's' : ''} ${formData.children > 0 ? `+ ${formData.children} Child${formData.children > 1 ? 'ren' : ''}` : ''}</div>
                </div>

                ${formData.travelDate ? `
                <div class="field">
                    <span class="label">📅 Preferred Travel Date:</span>
                    <div class="value">${new Date(formData.travelDate).toLocaleDateString()}</div>
                </div>
                ` : ''}

                ${formData.duration ? `
                <div class="field">
                    <span class="label">⏰ Duration:</span>
                    <div class="value">${formData.duration} Days</div>
                </div>
                ` : ''}

                ${formData.budget ? `
                <div class="field">
                    <span class="label">💰 Budget Range:</span>
                    <div class="value">₹${formData.budget} per person</div>
                </div>
                ` : ''}

                ${formData.packageName ? `
                <div class="field">
                    <span class="label">📦 Selected Package:</span>
                    <div class="value">${formData.packageName}</div>
                </div>
                ` : ''}

                ${formData.message ? `
                <div class="field">
                    <span class="label">💬 Special Requirements/Message:</span>
                    <div class="value">${formData.message}</div>
                </div>
                ` : ''}

                <div class="highlight">
                    <strong>⚡ Action Required:</strong><br>
                    Please contact this customer within 24 hours to provide a customized quote and itinerary.
                </div>
            </div>

            <div class="footer">
                <p>This email was sent from the Maa Laxmi Tours website contact form.</p>
                <p><strong>Maa Laxmi Tour & Travels</strong><br>
                Your Trusted Partner for Andaman Adventures</p>
            </div>
        </div>
    </body>
    </html>
  `;
};

// Alternative service for EmailJS (client-side email service)
export const sendEmailWithEmailJS = async (formData) => {
    // You'll need to install emailjs: npm install @emailjs/browser
    // And import it: import emailjs from '@emailjs/browser';

    try {
        const templateParams = {
            from_name: formData.name,
            from_email: formData.email,
            phone: formData.phone,
            adults: formData.adults,
            children: formData.children,
            travel_date: formData.travelDate,
            duration: formData.duration,
            budget: formData.budget,
            package_name: formData.packageName,
            message: formData.message,
            to_email: '<EMAIL>' // Replace with your email
        };

        // Replace these with your EmailJS credentials
        const result = await emailjs.send(
            'YOUR_SERVICE_ID',
            'YOUR_TEMPLATE_ID',
            templateParams,
            'YOUR_PUBLIC_KEY'
        );

        return result;
    } catch (error) {
        console.error('EmailJS error:', error);
        throw error;
    }
};

export default {
    sendEmail,
    sendEmailWithEmailJS
};