import React from 'react';

const ModernCard = ({ 
  image, 
  duration, 
  price, 
  title, 
  subtitle, 
  includes, 
  onBookNow, 
  className = "",
  type = "package" // package, activity, ferry
}) => {
  return (
    <div className={`relative group overflow-hidden rounded-3xl shadow-xl hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2 ${className}`}>
      {/* Background Image */}
      <div className="relative h-80 overflow-hidden">
        <img 
          src={image} 
          alt={title}
          className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110"
        />
        {/* Gradient Overlay */}
        <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/20 to-transparent"></div>
        
        {/* Duration Badge */}
        <div className="absolute top-6 left-6">
          <span className="bg-golden-sand text-deep-navy text-sm font-bold px-4 py-2 rounded-full shadow-lg">
            {duration}
          </span>
        </div>

        {/* Book Now Button - Right Side */}
        <div className="absolute top-6 right-6">
          <button 
            onClick={onBookNow}
            className="bg-sunset-orange hover:bg-sunset-orange/90 text-white px-6 py-2 rounded-full font-semibold text-sm transition-all duration-300 transform hover:scale-105 shadow-lg"
          >
            Book Now
          </button>
        </div>

        {/* Content */}
        <div className="absolute bottom-0 left-0 right-0 p-6 text-white">
          {/* Price */}
          <div className="mb-3">
            <p className="text-sm opacity-90">Starts from:</p>
            <p className="text-2xl font-bold">{price} <span className="text-sm font-normal opacity-80">/per head</span></p>
          </div>

          {/* Title */}
          <h3 className="text-xl font-bold mb-2 leading-tight">{title}</h3>
          
          {/* Subtitle */}
          {subtitle && (
            <p className="text-sm opacity-90 mb-3">{subtitle}</p>
          )}

          {/* Includes */}
          <div className="text-sm opacity-90">
            <span className="font-semibold">Inclusion: </span>
            {includes}
          </div>
        </div>
      </div>

      {/* Hover Effect Overlay */}
      <div className="absolute inset-0 bg-gradient-to-t from-ocean-blue/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
    </div>
  );
};

export default ModernCard;