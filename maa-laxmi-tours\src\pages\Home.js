import React, {
  useState
} from 'react';
import {
  Link
} from 'react-router-dom';
import QueryForm from '../components/QueryForm';
import SEO from '../components/SEO';
import HeroSection from '../components/HeroSection';
import ModernCard from '../components/ModernCard';

const Home = () => {
  const [showQueryForm, setShowQueryForm] = useState(false);

  const featuredPackages = [{
      id: 1,
      title: "Exotic Honeymoon",
      subtitle: "holiday package",
      price: "₹24000.00",
      duration: "05 Nights & 06 Days",
      image: "https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80",
      includes: "Accommodation, Breakfast, Private Cruise, A/c <PERSON><PERSON>"
    },
    {
      id: 2,
      title: "Mesmerizing",
      subtitle: "Honeymoon trip",
      price: "₹28000.00",
      duration: "04 Nights & 05 Days",
      image: "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80",
      includes: "Accommodation, Breakfast, Private Cruise, A/c Cab"
    },
    {
      id: 3,
      title: "Dazzle andaman",
      subtitle: "honeymoon",
      price: "₹28000.00",
      duration: "05 Nights & 06 Days",
      image: "https://images.unsplash.com/photo-1559827260-dc66d52bef19?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80",
      includes: "Accommodation, Breakfast, Private Cruise, A/c Cab"
    }
  ];

  const popularActivities = [{
      id: 1,
      title: "Scuba Diving",
      subtitle: "Underwater Adventure",
      price: "₹3500.00",
      duration: "3-4 Hours",
      image: "https://images.unsplash.com/photo-1544551763-77ef2d0cfc6c?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80",
      includes: "Equipment, Instructor, Underwater Photos, Certificate"
    },
    {
      id: 2,
      title: "Parasailing",
      subtitle: "Sky High Adventure",
      price: "₹2500.00",
      duration: "30 Minutes",
      image: "https://images.unsplash.com/photo-1530549387789-4c1017266635?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80",
      includes: "Safety Equipment, Professional Operator, Photos"
    },
    {
      id: 3,
      title: "Sea Walking",
      subtitle: "Ocean Floor Experience",
      price: "₹4000.00",
      duration: "1 Hour",
      image: "https://images.unsplash.com/photo-1583212292454-1fe6229603b7?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80",
      includes: "Special Helmet, Guide, Underwater Photos"
    }
  ];

  const destinations = [{
      name: "Havelock Island",
      image: "https://images.unsplash.com/photo-1578662996442-48f60103fc96?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80",
      description: "Famous for Radhanagar Beach and crystal clear waters",
      attractions: "5+ Attractions"
    },
    {
      name: "Neil Island",
      image: "https://images.unsplash.com/photo-1559827260-dc66d52bef19?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80",
      description: "Peaceful island with natural rock formations",
      attractions: "3+ Attractions"
    },
    {
      name: "Port Blair",
      image: "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80",
      description: "Capital city with rich history and culture",
      attractions: "8+ Attractions"
    },
    {
      name: "Ross Island",
      image: "https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80",
      description: "Historical ruins and colonial architecture",
      attractions: "4+ Attractions"
    }
  ];

  const testimonials = [{
      name: "Rahul & Priya Sharma",
      location: "Mumbai",
      text: "Our honeymoon in Andaman was absolutely magical! Maa Laxmi Tours made everything perfect from start to finish. The private beach dinner was unforgettable.",
      rating: 5,
      image: "https://images.unsplash.com/photo-1494790108755-2616b612b786?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=150&q=80",
      package: "Honeymoon Package"
    },
    {
      name: "Amit Kumar Family",
      location: "Delhi",
      text: "Best family vacation ever! Kids loved the water sports and we enjoyed the peaceful beaches. Excellent service and great value for money.",
      rating: 5,
      image: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=150&q=80",
      package: "Family Package"
    },
    {
      name: "TechCorp Team",
      location: "Bangalore",
      text: "Professional service for our corporate outing. Great team building activities and seamless coordination. Highly recommended for group tours.",
      rating: 5,
      image: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=150&q=80",
      package: "Corporate Package"
    }
  ];

  const whyChooseUs = [{
      icon: "🏆",
      title: "Award Winning Service",
      description: "Recognized as the best tour operator in Andaman for 3 consecutive years",
      color: "from-golden-sand to-sunset-orange"
    },
    {
      icon: "🌊",
      title: "Local Expertise",
      description: "Deep knowledge of hidden gems and authentic local experiences",
      color: "from-turquoise-aqua to-ocean-blue"
    },
    {
      icon: "💯",
      title: "100% Satisfaction",
      description: "Over 1000+ happy customers with 5-star reviews and testimonials",
      color: "from-sunset-orange to-golden-sand"
    },
    {
      icon: "🚁",
      title: "Exclusive Experiences",
      description: "Unique activities and premium services not available elsewhere",
      color: "from-ocean-blue to-turquoise-aqua"
    }
  ];

  return ( <
    div className = "min-h-screen bg-soft-white" >
    <
    SEO title = "Maa Laxmi Tour & Travels - Best Andaman Tour Packages | Honeymoon & Family Tours"
    description = "Discover paradise with Maa Laxmi Tour & Travels. Best Andaman honeymoon packages, family tours, ferry booking, and water sports. Book your dream vacation today!"
    keywords = "Andaman tour packages, honeymoon packages Andaman, family tours Andaman, Havelock Island tours, Neil Island packages, Port Blair tours, ferry booking Andaman, water sports Andaman" /
    >

    {
      /* Hero Section */ } <
    HeroSection onShowQueryForm = {
      () => setShowQueryForm(true)
    }
    />

    {
      /* About Section */ } <
    section className = "py-20 bg-gradient-to-br from-soft-white to-blue-50" >
    <
    div className = "container mx-auto px-4" >
    <
    div className = "grid grid-cols-1 lg:grid-cols-2 gap-16 items-center" >
    <
    div className = "space-y-8" >
    <
    div >
    <
    div className = "inline-flex items-center bg-ocean-blue/10 rounded-full px-4 py-2 mb-4" >
    <
    span className = "text-ocean-blue font-semibold text-sm" > About Us < /span> <
    /div> <
    h2 className = "text-4xl lg:text-5xl font-bold text-deep-navy mb-6" >
    Your Trusted Partner
    for <
    span className = "text-transparent bg-clip-text bg-gradient-to-r from-ocean-blue to-turquoise-aqua" > Andaman Adventures < /span> <
    /h2> <
    p className = "text-lg text-gray-600 leading-relaxed" >
    With over a decade of experience in Andaman tourism, we are your trusted partner
    for creating unforgettable memories.Our team of local experts ensures you experience the best of what these beautiful islands have to offer. <
    /p> <
    /div>

    <
    div className = "grid grid-cols-2 gap-8" >
    <
    div className = "text-center p-6 bg-white rounded-2xl shadow-lg" >
    <
    div className = "text-4xl font-bold text-ocean-blue mb-2" > 1000 + < /div> <
    div className = "text-gray-600" > Happy Customers < /div> <
    /div> <
    div className = "text-center p-6 bg-white rounded-2xl shadow-lg" >
    <
    div className = "text-4xl font-bold text-ocean-blue mb-2" > 50 + < /div> <
    div className = "text-gray-600" > Tour Packages < /div> <
    /div> <
    div className = "text-center p-6 bg-white rounded-2xl shadow-lg" >
    <
    div className = "text-4xl font-bold text-ocean-blue mb-2" > 24 / 7 < /div> <
    div className = "text-gray-600" > Customer Support < /div> <
    /div> <
    div className = "text-center p-6 bg-white rounded-2xl shadow-lg" >
    <
    div className = "text-4xl font-bold text-ocean-blue mb-2" > 4.9★ < /div> <
    div className = "text-gray-600" > Rating < /div> <
    /div> <
    /div> <
    /div>

    <
    div className = "relative" >
    <
    div className = "absolute -top-4 -left-4 w-72 h-72 bg-gradient-to-r from-turquoise-aqua/20 to-ocean-blue/20 rounded-full blur-3xl" > < /div> <
    img src = "https://images.unsplash.com/photo-1559827260-dc66d52bef19?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80"
    alt = "About Us"
    className = "relative rounded-3xl shadow-2xl" /
    >
    <
    /div> <
    /div> <
    /div> <
    /section>

    {
      /* Featured Packages */ } <
    section className = "py-20 bg-gradient-to-br from-gray-50 to-blue-50" >
    <
    div className = "container mx-auto px-4" >
    <
    div className = "text-center mb-16" >
    <
    div className = "inline-flex items-center bg-sunset-orange/10 rounded-full px-4 py-2 mb-4" >
    <
    span className = "text-sunset-orange font-semibold text-sm" > Featured Packages < /span> <
    /div> <
    h2 className = "text-4xl lg:text-5xl font-bold text-deep-navy mb-6" >
    Handpicked Tours
    for <
    span className = "text-transparent bg-clip-text bg-gradient-to-r from-sunset-orange to-golden-sand" > Perfect Experience < /span> <
    /h2> <
    p className = "text-lg text-gray-600 max-w-2xl mx-auto" >
    Discover our most popular tour packages designed to give you the best of Andaman Islands <
    /p> <
    /div>

    <
    div className = "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8" > {
      featuredPackages.map(pkg => ( <
        ModernCard key = {
          pkg.id
        }
        image = {
          pkg.image
        }
        duration = {
          pkg.duration
        }
        price = {
          pkg.price
        }
        title = {
          pkg.title
        }
        subtitle = {
          pkg.subtitle
        }
        includes = {
          pkg.includes
        }
        onBookNow = {
          () => setShowQueryForm(true)
        }
        />
      ))
    } <
    /div>

    <
    div className = "text-center mt-12" >
    <
    Link to = "/tours"
    className = "inline-flex items-center bg-gradient-to-r from-sunset-orange to-golden-sand text-white px-8 py-4 rounded-full font-bold text-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105" >
    View All Packages <
    svg className = "w-5 h-5 ml-2"
    fill = "none"
    stroke = "currentColor"
    viewBox = "0 0 24 24" >
    <
    path strokeLinecap = "round"
    strokeLinejoin = "round"
    strokeWidth = {
      2
    }
    d = "M13 7l5 5m0 0l-5 5m5-5H6" / >
    <
    /svg> <
    /Link> <
    /div> <
    /div> <
    /section>

    {
      /* Popular Activities */ } <
    section className = "py-20 bg-soft-white" >
    <
    div className = "container mx-auto px-4" >
    <
    div className = "text-center mb-16" >
    <
    div className = "inline-flex items-center bg-turquoise-aqua/10 rounded-full px-4 py-2 mb-4" >
    <
    span className = "text-turquoise-aqua font-semibold text-sm" > Adventure Activities < /span> <
    /div> <
    h2 className = "text-4xl lg:text-5xl font-bold text-deep-navy mb-6" >
    Thrilling <
    span className = "text-transparent bg-clip-text bg-gradient-to-r from-turquoise-aqua to-ocean-blue" > Water Adventures < /span> <
    /h2> <
    p className = "text-lg text-gray-600 max-w-2xl mx-auto" >
    Experience the underwater world and aerial thrills with our exciting activity packages <
    /p> <
    /div>

    <
    div className = "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8" > {
      popularActivities.map(activity => ( <
        ModernCard key = {
          activity.id
        }
        image = {
          activity.image
        }
        duration = {
          activity.duration
        }
        price = {
          activity.price
        }
        title = {
          activity.title
        }
        subtitle = {
          activity.subtitle
        }
        includes = {
          activity.includes
        }
        onBookNow = {
          () => setShowQueryForm(true)
        }
        type = "activity" /
        >
      ))
    } <
    /div>

    <
    div className = "text-center mt-12" >
    <
    Link to = "/activities"
    className = "inline-flex items-center bg-gradient-to-r from-turquoise-aqua to-ocean-blue text-white px-8 py-4 rounded-full font-bold text-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105" >
    View All Activities <
    svg className = "w-5 h-5 ml-2"
    fill = "none"
    stroke = "currentColor"
    viewBox = "0 0 24 24" >
    <
    path strokeLinecap = "round"
    strokeLinejoin = "round"
    strokeWidth = {
      2
    }
    d = "M13 7l5 5m0 0l-5 5m5-5H6" / >
    <
    /svg> <
    /Link> <
    /div> <
    /div> <
    /section>

    {
      /* Ferry Services Section */ } <
    section className = "py-20 bg-gradient-to-br from-blue-50 to-gray-50" >
    <
    div className = "container mx-auto px-4" >
    <
    div className = "text-center mb-16" >
    <
    div className = "inline-flex items-center bg-ocean-blue/10 rounded-full px-4 py-2 mb-4" >
    <
    span className = "text-ocean-blue font-semibold text-sm" > Ferry Services < /span> <
    /div> <
    h2 className = "text-4xl lg:text-5xl font-bold text-deep-navy mb-6" >
    Convenient <
    span className = "text-transparent bg-clip-text bg-gradient-to-r from-ocean-blue to-turquoise-aqua" > Island Hopping < /span> <
    /h2> <
    p className = "text-lg text-gray-600 max-w-2xl mx-auto" >
    Comfortable and reliable ferry services connecting all major islands in Andaman <
    /p> <
    /div>

    {
      /* Ferry Cards Grid */ } <
    div className = "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8" > {
      [{
          name: "Makruzz",
          subtitle: "premium ferry service",
          route: "Port Blair ↔ Havelock",
          duration: "1.5 Hours",
          image: "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80",
          includes: "Air Conditioned, Comfortable Seating, Life Jackets, Refreshments"
        },
        {
          name: "Green Ocean",
          subtitle: "economy ferry service",
          route: "Port Blair ↔ Havelock",
          duration: "2 Hours",
          image: "https://images.unsplash.com/photo-1559827260-dc66d52bef19?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80",
          includes: "Economy Class, Basic Seating, Life Jackets, Snacks Available"
        },
        {
          name: "Coastal Cruise",
          subtitle: "island hopping service",
          route: "Port Blair ↔ Neil Island",
          duration: "1 Hour",
          image: "https://images.unsplash.com/photo-1578662996442-48f60103fc96?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80",
          includes: "Premium Service, Comfortable Seating, Life Jackets, Complimentary Water"
        },
        {
          name: "Sea Link",
          subtitle: "inter-island express",
          route: "Havelock ↔ Neil Island",
          duration: "45 Minutes",
          image: "https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80",
          includes: "Inter-Island Service, Quick Transit, Life Jackets, Basic Amenities"
        }
      ].map((ferry, index) => ( <
        div key = {
          index
        }
        className = "relative group overflow-hidden rounded-3xl shadow-xl hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2" > {
          /* Background Image */ } <
        div className = "relative h-80 overflow-hidden" >
        <
        img src = {
          ferry.image
        }
        alt = {
          ferry.name
        }
        className = "w-full h-full object-cover transition-transform duration-700 group-hover:scale-110" /
        > {
          /* Gradient Overlay */ } <
        div className = "absolute inset-0 bg-gradient-to-t from-black/80 via-black/20 to-transparent" > < /div>

        {
          /* Duration Badge */ } <
        div className = "absolute top-6 left-6" >
        <
        span className = "bg-golden-sand text-deep-navy text-sm font-bold px-4 py-2 rounded-full shadow-lg" > {
          ferry.duration
        } <
        /span> <
        /div>

        {
          /* Book Now Button - Right Side */ } <
        div className = "absolute top-6 right-6" >
        <
        button onClick = {
          () => setShowQueryForm(true)
        }
        className = "bg-sunset-orange hover:bg-sunset-orange/90 text-white px-6 py-2 rounded-full font-semibold text-sm transition-all duration-300 transform hover:scale-105 shadow-lg" >
        Book Now <
        /button> <
        /div>

        {
          /* Content */ } <
        div className = "absolute bottom-0 left-0 right-0 p-6 text-white" > {
          /* Route */ } <
        div className = "mb-3" >
        <
        p className = "text-sm opacity-90" > Route: < /p> <
        p className = "text-lg font-semibold" > {
          ferry.route
        } < /p> <
        /div>

        {
          /* Title */ } <
        h3 className = "text-xl font-bold mb-2 leading-tight" > {
          ferry.name
        } < /h3>

        {
          /* Subtitle */ } <
        p className = "text-sm opacity-90 mb-3" > {
          ferry.subtitle
        } < /p>

        {
          /* Includes */ } <
        div className = "text-sm opacity-90" >
        <
        span className = "font-semibold" > Includes: < /span> {
          ferry.includes
        } <
        /div> <
        /div> <
        /div>

        {
          /* Hover Effect Overlay */ } <
        div className = "absolute inset-0 bg-gradient-to-t from-ocean-blue/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" > < /div> <
        /div>
      ))
    } <
    /div>

    <
    div className = "text-center mt-12" >
    <
    Link to = "/ferry"
    className = "inline-flex items-center bg-gradient-to-r from-ocean-blue to-turquoise-aqua text-white px-8 py-4 rounded-full font-bold text-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105" >
    View All Ferry Services <
    svg className = "w-5 h-5 ml-2"
    fill = "none"
    stroke = "currentColor"
    viewBox = "0 0 24 24" >
    <
    path strokeLinecap = "round"
    strokeLinejoin = "round"
    strokeWidth = {
      2
    }
    d = "M13 7l5 5m0 0l-5 5m5-5H6" / >
    <
    /svg> <
    /Link> <
    /div> <
    /div> <
    /section>

    {
      /* Destinations */ } <
    section className = "py-20 bg-soft-white" >
    <
    div className = "container mx-auto px-4" >
    <
    div className = "text-center mb-16" >
    <
    div className = "inline-flex items-center bg-golden-sand/10 rounded-full px-4 py-2 mb-4" >
    <
    span className = "text-golden-sand font-semibold text-sm" > Top Destinations < /span> <
    /div> <
    h2 className = "text-4xl lg:text-5xl font-bold text-deep-navy mb-6" >
    Explore Beautiful <
    span className = "text-transparent bg-clip-text bg-gradient-to-r from-golden-sand to-sunset-orange" > Islands < /span> <
    /h2> <
    p className = "text-lg text-gray-600 max-w-2xl mx-auto" >
    Discover the most stunning destinations that make Andaman a tropical paradise <
    /p> <
    /div>

    <
    div className = "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6" > {
      destinations.map((destination, index) => ( <
        div key = {
          index
        }
        className = "group bg-white rounded-2xl shadow-lg overflow-hidden hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2" >
        <
        div className = "relative h-48 overflow-hidden" >
        <
        img src = {
          destination.image
        }
        alt = {
          destination.name
        }
        className = "w-full h-full object-cover transition-transform duration-300 group-hover:scale-110" /
        >
        <
        div className = "absolute inset-0 bg-gradient-to-t from-black/50 to-transparent" > < /div> <
        div className = "absolute bottom-4 left-4 text-white" >
        <
        div className = "text-xs bg-golden-sand text-deep-navy px-2 py-1 rounded-full font-semibold mb-2" > {
          destination.attractions
        } <
        /div> <
        /div> <
        /div> <
        div className = "p-6" >
        <
        h3 className = "text-xl font-bold text-deep-navy mb-2" > {
          destination.name
        } < /h3> <
        p className = "text-gray-600 text-sm" > {
          destination.description
        } < /p> <
        /div> <
        /div>
      ))
    } <
    /div> <
    /div> <
    /section>

    {
      /* Why Choose Us */ } <
    section className = "py-20 bg-gradient-to-br from-gray-50 to-blue-50" >
    <
    div className = "container mx-auto px-4" >
    <
    div className = "text-center mb-16" >
    <
    div className = "inline-flex items-center bg-ocean-blue/10 rounded-full px-4 py-2 mb-4" >
    <
    span className = "text-ocean-blue font-semibold text-sm" > Why Choose Us < /span> <
    /div> <
    h2 className = "text-4xl lg:text-5xl font-bold text-deep-navy mb-6" >
    What Makes Us <
    span className = "text-transparent bg-clip-text bg-gradient-to-r from-ocean-blue to-turquoise-aqua" > Different < /span> <
    /h2> <
    /div>

    <
    div className = "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8" > {
      whyChooseUs.map((item, index) => ( <
        div key = {
          index
        }
        className = "group text-center p-8 bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2" >
        <
        div className = {
          `w-20 h-20 bg-gradient-to-r ${item.color} rounded-2xl flex items-center justify-center mx-auto mb-6 text-3xl group-hover:scale-110 transition-transform duration-300`
        } > {
          item.icon
        } <
        /div> <
        h3 className = "text-xl font-bold text-deep-navy mb-4" > {
          item.title
        } < /h3> <
        p className = "text-gray-600" > {
          item.description
        } < /p> <
        /div>
      ))
    } <
    /div> <
    /div> <
    /section>

    {
      /* Testimonials */ } <
    section className = "py-20 bg-soft-white" >
    <
    div className = "container mx-auto px-4" >
    <
    div className = "text-center mb-16" >
    <
    div className = "inline-flex items-center bg-sunset-orange/10 rounded-full px-4 py-2 mb-4" >
    <
    span className = "text-sunset-orange font-semibold text-sm" > Testimonials < /span> <
    /div> <
    h2 className = "text-4xl lg:text-5xl font-bold text-deep-navy mb-6" >
    What Our <
    span className = "text-transparent bg-clip-text bg-gradient-to-r from-sunset-orange to-golden-sand" > Customers Say < /span> <
    /h2> <
    p className = "text-lg text-gray-600 max-w-2xl mx-auto" >
    Real experiences from real travelers who chose us
    for their Andaman adventure <
    /p> <
    /div>

    <
    div className = "grid grid-cols-1 md:grid-cols-3 gap-8" > {
      testimonials.map((testimonial, index) => ( <
        div key = {
          index
        }
        className = "bg-white p-8 rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300" >
        <
        div className = "flex items-center mb-6" >
        <
        img src = {
          testimonial.image
        }
        alt = {
          testimonial.name
        }
        className = "w-16 h-16 rounded-full object-cover mr-4" /
        >
        <
        div >
        <
        h4 className = "font-bold text-deep-navy" > {
          testimonial.name
        } < /h4> <
        p className = "text-sm text-gray-500" > {
          testimonial.location
        } < /p> <
        div className = "text-xs bg-golden-sand/20 text-golden-sand px-2 py-1 rounded-full mt-1 inline-block" > {
          testimonial.package
        } <
        /div> <
        /div> <
        /div>

        <
        div className = "flex mb-4" > {
          [...Array(testimonial.rating)].map((_, i) => ( <
            svg key = {
              i
            }
            className = "w-5 h-5 text-golden-sand"
            fill = "currentColor"
            viewBox = "0 0 20 20" >
            <
            path d = "M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" / >
            <
            /svg>
          ))
        } <
        /div>

        <
        p className = "text-gray-600 italic" > "{testimonial.text}" < /p> <
        /div>
      ))
    } <
    /div> <
    /div> <
    /section>

    {
      /* CTA Section */ } <
    section className = "py-20 bg-gradient-to-r from-ocean-blue via-turquoise-aqua to-ocean-blue text-white relative overflow-hidden" >
    <
    div className = "absolute inset-0 bg-black/20" > < /div> <
    div className = "absolute top-10 left-10 w-32 h-32 bg-golden-sand/20 rounded-full blur-3xl" > < /div> <
    div className = "absolute bottom-10 right-10 w-40 h-40 bg-sunset-orange/20 rounded-full blur-3xl" > < /div>

    <
    div className = "relative container mx-auto px-4 text-center" >
    <
    h2 className = "text-4xl lg:text-5xl font-bold mb-6" >
    Ready
    for Your <
    span className = "text-golden-sand" > Andaman Adventure ? < /span> <
    /h2> <
    p className = "text-xl mb-10 max-w-2xl mx-auto" >
    Let us create the perfect itinerary
    for your dream vacation.Get in touch with our travel experts today!
    <
    /p>

    <
    div className = "flex flex-col sm:flex-row gap-6 justify-center" >
    <
    button onClick = {
      () => setShowQueryForm(true)
    }
    className = "bg-gradient-to-r from-sunset-orange to-golden-sand text-white px-10 py-4 rounded-full font-bold text-lg hover:shadow-2xl transition-all duration-300 transform hover:scale-105" >
    Get Custom Quote <
    /button> <
    Link to = "/contact"
    className = "border-2 border-white text-white px-10 py-4 rounded-full font-bold text-lg hover:bg-white hover:text-ocean-blue transition-all duration-300" >
    Contact Us <
    /Link> <
    /div> <
    /div> <
    /section>

    {
      /* Query Form Modal */ } {
      showQueryForm && ( <
        QueryForm title = "Plan Your Andaman Trip"
        onClose = {
          () => setShowQueryForm(false)
        }
        />
      )
    } <
    /div>
  );
};

export default Home;