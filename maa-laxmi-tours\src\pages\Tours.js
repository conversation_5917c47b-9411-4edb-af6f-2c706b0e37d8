import React, { useState } from 'react';
import { Routes, Route, Link, useLocation } from 'react-router-dom';
import QueryForm from '../components/QueryForm';
import ModernCard from '../components/ModernCard';
import SEO from '../components/SEO';

const HoneymoonTours = ({ onBookNow }) => {
  const tours = [
    {
      id: 1,
      title: "Exotic Honeymoon",
      subtitle: "holiday package",
      price: "₹24000.00",
      duration: "05 Nights & 06 Days",
      image: "https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80",
      includes: "Accommodation, Breakfast, Private Cruise, A/c Cab"
    },
    {
      id: 2,
      title: "Mesmerizing",
      subtitle: "Honeymoon trip",
      price: "₹28000.00",
      duration: "04 Nights & 05 Days",
      image: "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80",
      includes: "Accommodation, Breakfast, Private Cruise, A/c Cab"
    },
    {
      id: 3,
      title: "Dazzle andaman",
      subtitle: "honeymoon",
      price: "₹28000.00",
      duration: "05 Nights & 06 Days",
      image: "https://images.unsplash.com/photo-1559827260-dc66d52bef19?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80",
      includes: "Accommodation, Breakfast, Private Cruise, A/c Cab"
    },
    {
      id: 4,
      title: "Romantic Paradise",
      subtitle: "luxury honeymoon",
      price: "₹35000.00",
      duration: "06 Nights & 07 Days",
      image: "https://images.unsplash.com/photo-1578662996442-48f60103fc96?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80",
      includes: "Luxury Resort, All Meals, Spa Session, Private Tours"
    }
  ];

  return (
    <div className="min-h-screen bg-soft-white">
      <SEO 
        title="Honeymoon Tour Packages Andaman - Romantic Getaways | Maa Laxmi Tours"
        description="Romantic honeymoon packages in Andaman with private beaches, luxury resorts, and unforgettable experiences. Book your dream honeymoon today!"
      />
      
      {/* Hero Section */}
      <section className="relative py-20 bg-gradient-to-r from-pink-500 via-red-500 to-orange-500 text-white overflow-hidden">
        <div className="absolute inset-0 bg-black/30"></div>
        <div className="absolute top-10 left-10 w-32 h-32 bg-white/10 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-10 right-10 w-40 h-40 bg-white/10 rounded-full blur-3xl animate-pulse delay-1000"></div>
        
        <div className="relative container mx-auto px-4 text-center">
          <div className="inline-flex items-center bg-white/20 backdrop-blur-sm rounded-full px-6 py-3 mb-6">
            <span className="text-white font-semibold">💕 Honeymoon Packages</span>
          </div>
          <h1 className="text-5xl lg:text-6xl font-bold mb-6">
            Romantic 
            <span className="text-golden-sand"> Honeymoon</span> Tours
          </h1>
          <p className="text-xl max-w-3xl mx-auto">
            Create magical memories with your loved one in the pristine paradise of Andaman Islands
          </p>
        </div>
      </section>
      
      {/* Tours Grid */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-deep-navy mb-4">Choose Your Perfect Honeymoon</h2>
            <p className="text-lg text-gray-600">Handcrafted romantic experiences for couples</p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {tours.map((tour) => (
              <Link key={tour.id} to={`/package/${tour.id}`}>
                <ModernCard
                  image={tour.image}
                  duration={tour.duration}
                  price={tour.price}
                  title={tour.title}
                  subtitle={tour.subtitle}
                  includes={tour.includes}
                  onBookNow={() => onBookNow(tour.title)}
                />
              </Link>
            ))}
          </div>
        </div>
      </section>

      {/* Why Choose Honeymoon Section */}
      <section className="py-20 bg-gradient-to-br from-pink-50 to-red-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-deep-navy mb-6">Why Choose Our Honeymoon Packages?</h2>
            <p className="text-lg text-gray-600">Special touches that make your honeymoon unforgettable</p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {[
              { icon: "🌹", title: "Romantic Settings", desc: "Private beach dinners and sunset cruises" },
              { icon: "🏖️", title: "Luxury Resorts", desc: "Premium beachfront accommodations" },
              { icon: "📸", title: "Photography", desc: "Professional couple photoshoot included" },
              { icon: "💆‍♀️", title: "Spa Treatments", desc: "Relaxing couple spa sessions" }
            ].map((feature, index) => (
              <div key={index} className="text-center p-6 bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300">
                <div className="text-4xl mb-4">{feature.icon}</div>
                <h3 className="text-xl font-bold text-deep-navy mb-2">{feature.title}</h3>
                <p className="text-gray-600">{feature.desc}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Romantic Destinations */}
      <section className="py-20 bg-soft-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-deep-navy mb-6">Romantic Destinations</h2>
            <p className="text-lg text-gray-600">Most romantic spots in Andaman for couples</p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {[
              { name: "Radhanagar Beach", image: "https://images.unsplash.com/photo-1578662996442-48f60103fc96?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80", desc: "Perfect for romantic sunsets" },
              { name: "Elephant Beach", image: "https://images.unsplash.com/photo-1559827260-dc66d52bef19?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80", desc: "Secluded beach for couples" },
              { name: "Neil Island", image: "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80", desc: "Peaceful island getaway" }
            ].map((destination, index) => (
              <div key={index} className="group relative overflow-hidden rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300">
                <div className="h-64 overflow-hidden">
                  <img 
                    src={destination.image} 
                    alt={destination.name}
                    className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-110"
                  />
                </div>
                <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent"></div>
                <div className="absolute bottom-6 left-6 text-white">
                  <h3 className="text-xl font-bold mb-2">{destination.name}</h3>
                  <p className="text-sm opacity-90">{destination.desc}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Testimonials */}
      <section className="py-20 bg-gradient-to-br from-pink-50 to-red-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-deep-navy mb-6">Happy Couples</h2>
            <p className="text-lg text-gray-600">What our honeymooners say about us</p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {[
              { name: "Rahul & Priya", text: "Perfect honeymoon! Every detail was taken care of. The romantic dinner on the beach was magical.", rating: 5, image: "https://images.unsplash.com/photo-1494790108755-2616b612b786?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=150&q=80" },
              { name: "Amit & Sneha", text: "Amazing experience! The photography session captured our love beautifully. Highly recommended!", rating: 5, image: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=150&q=80" },
              { name: "Vikram & Riya", text: "The luxury resort and spa treatments made our honeymoon unforgettable. Thank you!", rating: 5, image: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=150&q=80" }
            ].map((testimonial, index) => (
              <div key={index} className="bg-white p-6 rounded-2xl shadow-lg">
                <div className="flex items-center mb-4">
                  <img 
                    src={testimonial.image} 
                    alt={testimonial.name}
                    className="w-12 h-12 rounded-full object-cover mr-4"
                  />
                  <div>
                    <h4 className="font-bold text-deep-navy">{testimonial.name}</h4>
                    <div className="flex">
                      {[...Array(testimonial.rating)].map((_, i) => (
                        <svg key={i} className="w-4 h-4 text-golden-sand" fill="currentColor" viewBox="0 0 20 20">
                          <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                        </svg>
                      ))}
                    </div>
                  </div>
                </div>
                <p className="text-gray-600 italic">"{testimonial.text}"</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Planning Tips */}
      <section className="py-20 bg-soft-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-deep-navy mb-6">Honeymoon Planning Tips</h2>
            <p className="text-lg text-gray-600">Make your honeymoon planning stress-free</p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {[
              { icon: "📅", title: "Best Time", desc: "October to May for perfect weather" },
              { icon: "💰", title: "Budget Planning", desc: "₹20,000 - ₹50,000 per couple" },
              { icon: "📋", title: "Documents", desc: "Valid ID and marriage certificate" },
              { icon: "🎒", title: "Packing", desc: "Light clothes, swimwear, sunscreen" }
            ].map((tip, index) => (
              <div key={index} className="text-center p-6 bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300">
                <div className="text-4xl mb-4">{tip.icon}</div>
                <h3 className="text-xl font-bold text-deep-navy mb-2">{tip.title}</h3>
                <p className="text-gray-600">{tip.desc}</p>
              </div>
            ))}
          </div>
        </div>
      </section>
    </div>
  );
};

const FamilyTours = ({ onBookNow }) => {
  const tours = [
    {
      id: 1,
      title: "Family Fun",
      subtitle: "adventure package",
      price: "₹18000.00",
      duration: "04 Nights & 05 Days",
      image: "https://images.unsplash.com/photo-1559827260-dc66d52bef19?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80",
      includes: "Family Rooms, All Meals, Kid Activities, Beach Games"
    },
    {
      id: 2,
      title: "Complete Family",
      subtitle: "holiday package",
      price: "₹24000.00",
      duration: "05 Nights & 06 Days",
      image: "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80",
      includes: "Comfortable Stay, All Meals, Water Sports, Island Tours"
    },
    {
      id: 3,
      title: "Extended Family",
      subtitle: "vacation package",
      price: "₹30000.00",
      duration: "06 Nights & 07 Days",
      image: "https://images.unsplash.com/photo-1578662996442-48f60103fc96?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80",
      includes: "Premium Hotels, All Meals, Guided Tours, Entertainment"
    },
    {
      id: 4,
      title: "Ultimate Family",
      subtitle: "luxury package",
      price: "₹38000.00",
      duration: "07 Nights & 08 Days",
      image: "https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80",
      includes: "Luxury Stay, All Meals, Multiple Islands, Family Bonding"
    }
  ];

  return (
    <div className="min-h-screen bg-soft-white">
      <SEO 
        title="Family Tour Packages Andaman - Fun for All Ages | Maa Laxmi Tours"
        description="Perfect family vacation packages in Andaman with kid-friendly activities, comfortable stays, and memorable experiences for the whole family."
      />
      
      {/* Hero Section */}
      <section className="relative py-20 bg-gradient-to-r from-green-500 via-blue-500 to-purple-500 text-white overflow-hidden">
        <div className="absolute inset-0 bg-black/30"></div>
        <div className="absolute top-10 left-10 w-32 h-32 bg-white/10 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-10 right-10 w-40 h-40 bg-white/10 rounded-full blur-3xl animate-pulse delay-1000"></div>
        
        <div className="relative container mx-auto px-4 text-center">
          <div className="inline-flex items-center bg-white/20 backdrop-blur-sm rounded-full px-6 py-3 mb-6">
            <span className="text-white font-semibold">👨‍👩‍👧‍👦 Family Packages</span>
          </div>
          <h1 className="text-5xl lg:text-6xl font-bold mb-6">
            Perfect 
            <span className="text-golden-sand"> Family</span> Vacations
          </h1>
          <p className="text-xl max-w-3xl mx-auto">
            Fun-filled adventures and quality time for the whole family in Andaman's tropical paradise
          </p>
        </div>
      </section>
      
      {/* Tours Grid */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-deep-navy mb-4">Family-Friendly Adventures</h2>
            <p className="text-lg text-gray-600">Safe, fun, and memorable experiences for all ages</p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {tours.map((tour) => (
              <ModernCard
                key={tour.id}
                image={tour.image}
                duration={tour.duration}
                price={tour.price}
                title={tour.title}
                subtitle={tour.subtitle}
                includes={tour.includes}
                onBookNow={() => onBookNow(tour.title)}
              />
            ))}
          </div>
        </div>
      </section>
    </div>
  );
};

const GroupTours = ({ onBookNow }) => {
  const tours = [
    {
      id: 1,
      title: "Budget Group",
      subtitle: "package",
      price: "₹15000.00",
      duration: "04 Nights & 05 Days",
      image: "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80",
      includes: "Shared Rooms, All Meals, Group Activities, Transportation"
    },
    {
      id: 2,
      title: "Corporate Group",
      subtitle: "team building",
      price: "₹20000.00",
      duration: "05 Nights & 06 Days",
      image: "https://images.unsplash.com/photo-1559827260-dc66d52bef19?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80",
      includes: "Business Hotels, All Meals, Team Activities, Meeting Rooms"
    },
    {
      id: 3,
      title: "Premium Group",
      subtitle: "luxury package",
      price: "₹28000.00",
      duration: "06 Nights & 07 Days",
      image: "https://images.unsplash.com/photo-1578662996442-48f60103fc96?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80",
      includes: "Premium Hotels, All Meals, Private Transport, Group Discounts"
    },
    {
      id: 4,
      title: "Large Group",
      subtitle: "special package",
      price: "₹35000.00",
      duration: "07 Nights & 08 Days",
      image: "https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80",
      includes: "Group Hotels, All Meals, Flexible Itinerary, Special Rates"
    }
  ];

  return (
    <div className="min-h-screen bg-soft-white">
      <SEO 
        title="Group Tour Packages Andaman - Corporate & Friends | Maa Laxmi Tours"
        description="Special group tour packages for corporate outings, friends trips, and large groups with customized itineraries and group discounts."
      />
      
      {/* Hero Section */}
      <section className="relative py-20 bg-gradient-to-r from-purple-500 via-indigo-500 to-blue-500 text-white overflow-hidden">
        <div className="absolute inset-0 bg-black/30"></div>
        <div className="absolute top-10 left-10 w-32 h-32 bg-white/10 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-10 right-10 w-40 h-40 bg-white/10 rounded-full blur-3xl animate-pulse delay-1000"></div>
        
        <div className="relative container mx-auto px-4 text-center">
          <div className="inline-flex items-center bg-white/20 backdrop-blur-sm rounded-full px-6 py-3 mb-6">
            <span className="text-white font-semibold">👥 Group Packages</span>
          </div>
          <h1 className="text-5xl lg:text-6xl font-bold mb-6">
            Amazing 
            <span className="text-golden-sand"> Group</span> Tours
          </h1>
          <p className="text-xl max-w-3xl mx-auto">
            Special packages for groups, corporate outings, and team building experiences
          </p>
        </div>
      </section>
      
      {/* Tours Grid */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-deep-navy mb-4">Group Travel Made Easy</h2>
            <p className="text-lg text-gray-600">Customized packages with special group rates and flexible itineraries</p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {tours.map((tour) => (
              <ModernCard
                key={tour.id}
                image={tour.image}
                duration={tour.duration}
                price={tour.price}
                title={tour.title}
                subtitle={tour.subtitle}
                includes={tour.includes}
                onBookNow={() => onBookNow(tour.title)}
              />
            ))}
          </div>
        </div>
      </section>
    </div>
  );
};

const AdventureTours = ({ onBookNow }) => {
  const tours = [
    {
      id: 1,
      title: "Scuba Diving",
      subtitle: "adventure",
      price: "₹35000.00",
      duration: "04 Nights & 05 Days",
      image: "https://images.unsplash.com/photo-1544551763-77ef2d0cfc6c?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80",
      includes: "Diving Certification, Equipment, Underwater Photography"
    },
    {
      id: 2,
      title: "Trekking & Camping",
      subtitle: "wilderness",
      price: "₹22000.00",
      duration: "03 Nights & 04 Days",
      image: "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80",
      includes: "Camping Equipment, Trekking Guide, Meals, Safety Gear"
    },
    {
      id: 3,
      title: "Water Sports",
      subtitle: "extravaganza",
      price: "₹28000.00",
      duration: "05 Nights & 06 Days",
      image: "https://images.unsplash.com/photo-1530549387789-4c1017266635?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80",
      includes: "All Water Sports, Equipment, Professional Instructors"
    },
    {
      id: 4,
      title: "Island Hopping",
      subtitle: "adventure",
      price: "₹32000.00",
      duration: "06 Nights & 07 Days",
      image: "https://images.unsplash.com/photo-1559827260-dc66d52bef19?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80",
      includes: "Island Tours, Adventure Activities, Boat Transfers"
    }
  ];

  return (
    <div className="min-h-screen bg-soft-white">
      <SEO 
        title="Adventure Tour Packages Andaman - Thrilling Experiences | Maa Laxmi Tours"
        description="Exciting adventure packages in Andaman with scuba diving, trekking, water sports, and island hopping for thrill seekers."
      />
      
      {/* Hero Section */}
      <section className="relative py-20 bg-gradient-to-r from-orange-500 via-red-500 to-pink-500 text-white overflow-hidden">
        <div className="absolute inset-0 bg-black/30"></div>
        <div className="absolute top-10 left-10 w-32 h-32 bg-white/10 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-10 right-10 w-40 h-40 bg-white/10 rounded-full blur-3xl animate-pulse delay-1000"></div>
        
        <div className="relative container mx-auto px-4 text-center">
          <div className="inline-flex items-center bg-white/20 backdrop-blur-sm rounded-full px-6 py-3 mb-6">
            <span className="text-white font-semibold">🏄‍♂️ Adventure Packages</span>
          </div>
          <h1 className="text-5xl lg:text-6xl font-bold mb-6">
            Thrilling 
            <span className="text-golden-sand"> Adventure</span> Tours
          </h1>
          <p className="text-xl max-w-3xl mx-auto">
            Heart-pumping adventures and adrenaline-filled experiences for the brave souls
          </p>
        </div>
      </section>
      
      {/* Tours Grid */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-deep-navy mb-4">Adventure Awaits</h2>
            <p className="text-lg text-gray-600">Push your limits with our exciting adventure packages</p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {tours.map((tour) => (
              <ModernCard
                key={tour.id}
                image={tour.image}
                duration={tour.duration}
                price={tour.price}
                title={tour.title}
                subtitle={tour.subtitle}
                includes={tour.includes}
                onBookNow={() => onBookNow(tour.title)}
              />
            ))}
          </div>
        </div>
      </section>
    </div>
  );
};

const ToursOverview = ({ onBookNow }) => {
  const tourCategories = [
    {
      title: "Honeymoon Tours",
      description: "Romantic getaways for couples",
      image: "https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80",
      link: "/tours/honeymoon",
      color: "from-pink-500 to-red-500",
      icon: "💕"
    },
    {
      title: "Family Tours",
      description: "Fun-filled adventures for families",
      image: "https://images.unsplash.com/photo-1559827260-dc66d52bef19?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80",
      link: "/tours/family",
      color: "from-green-500 to-blue-500",
      icon: "👨‍👩‍👧‍👦"
    },
    {
      title: "Group Tours",
      description: "Special packages for groups",
      image: "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80",
      link: "/tours/group",
      color: "from-purple-500 to-indigo-500",
      icon: "👥"
    },
    {
      title: "Adventure Tours",
      description: "Thrilling experiences for adventurers",
      image: "https://images.unsplash.com/photo-1544551763-77ef2d0cfc6c?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80",
      link: "/tours/adventure",
      color: "from-orange-500 to-red-500",
      icon: "🏄‍♂️"
    }
  ];

  return (
    <div className="min-h-screen bg-soft-white">
      <SEO 
        title="Andaman Tour Packages - Honeymoon, Family, Group & Adventure | Maa Laxmi Tours"
        description="Explore our comprehensive range of Andaman tour packages including honeymoon, family, group, and adventure tours. Best prices and authentic experiences guaranteed."
      />
      
      {/* Hero Section */}
      <section className="relative py-20 bg-gradient-to-r from-ocean-blue via-turquoise-aqua to-ocean-blue text-white overflow-hidden">
        <div className="absolute inset-0 bg-black/30"></div>
        <div className="absolute top-10 left-10 w-32 h-32 bg-golden-sand/20 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-10 right-10 w-40 h-40 bg-sunset-orange/20 rounded-full blur-3xl animate-pulse delay-1000"></div>
        
        <div className="relative container mx-auto px-4 text-center">
          <div className="inline-flex items-center bg-white/20 backdrop-blur-sm rounded-full px-6 py-3 mb-6">
            <span className="text-white font-semibold">🏝️ Tour Packages</span>
          </div>
          <h1 className="text-5xl lg:text-7xl font-bold mb-6">
            Discover 
            <span className="text-golden-sand"> Andaman</span>
          </h1>
          <p className="text-xl max-w-3xl mx-auto">
            Carefully crafted tour packages designed for every type of traveler and every kind of adventure
          </p>
        </div>
      </section>

      {/* Tour Categories */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl lg:text-5xl font-bold text-deep-navy mb-6">
              Choose Your 
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-ocean-blue to-turquoise-aqua"> Perfect Adventure</span>
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              From romantic honeymoons to thrilling adventures, we have the perfect package for every traveler
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {tourCategories.map((category, index) => (
              <Link key={index} to={category.link} className="group">
                <div className="relative overflow-hidden rounded-3xl shadow-xl hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2">
                  <div className="relative h-80">
                    <img 
                      src={category.image} 
                      alt={category.title}
                      className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110"
                    />
                    <div className={`absolute inset-0 bg-gradient-to-t ${category.color} opacity-80`}></div>
                    
                    <div className="absolute inset-0 flex items-center justify-center text-white text-center p-8">
                      <div>
                        <div className="text-6xl mb-4">{category.icon}</div>
                        <h3 className="text-3xl font-bold mb-4">{category.title}</h3>
                        <p className="text-lg mb-6">{category.description}</p>
                        <div className="inline-flex items-center bg-white/20 backdrop-blur-sm rounded-full px-6 py-3 font-semibold group-hover:bg-white/30 transition-all">
                          Explore Packages
                          <svg className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                          </svg>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </Link>
            ))}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-gradient-to-br from-gray-50 to-blue-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-deep-navy mb-6">Why Choose Our Tour Packages?</h2>
            <p className="text-lg text-gray-600">Experience the difference with our premium services</p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {[
              { icon: "🏆", title: "Best Price Guarantee", desc: "Lowest prices with no hidden costs" },
              { icon: "🌟", title: "Expert Local Guides", desc: "Knowledgeable guides for authentic experiences" },
              { icon: "🛡️", title: "24/7 Support", desc: "Round-the-clock assistance during your trip" },
              { icon: "💯", title: "100% Satisfaction", desc: "Money-back guarantee if not satisfied" }
            ].map((feature, index) => (
              <div key={index} className="text-center p-6 bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2">
                <div className="text-4xl mb-4">{feature.icon}</div>
                <h3 className="text-xl font-bold text-deep-navy mb-2">{feature.title}</h3>
                <p className="text-gray-600">{feature.desc}</p>
              </div>
            ))}
          </div>
        </div>
      </section>
    </div>
  );
};

const Tours = () => {
  const [showQueryForm, setShowQueryForm] = useState(false);
  const [selectedPackage, setSelectedPackage] = useState('');

  const handleBookNow = (packageName) => {
    setSelectedPackage(packageName);
    setShowQueryForm(true);
  };

  return (
    <div>
      <Routes>
        <Route path="/" element={<ToursOverview onBookNow={handleBookNow} />} />
        <Route path="/honeymoon" element={<HoneymoonTours onBookNow={handleBookNow} />} />
        <Route path="/family" element={<FamilyTours onBookNow={handleBookNow} />} />
        <Route path="/group" element={<GroupTours onBookNow={handleBookNow} />} />
        <Route path="/adventure" element={<AdventureTours onBookNow={handleBookNow} />} />
      </Routes>

      {showQueryForm && (
        <QueryForm 
          title="Book Your Tour Package"
          packageName={selectedPackage}
          onClose={() => setShowQueryForm(false)}
        />
      )}
    </div>
  );
};

export default Tours;