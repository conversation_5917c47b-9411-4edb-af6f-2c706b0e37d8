// Simple Node.js backend for handling email sending
// This is an example server that you can deploy alongside your React app

const express = require('express');
const nodemailer = require('nodemailer');
const cors = require('cors');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 3001;

// Middleware
app.use(cors());
app.use(express.json());

// Email configuration
const transporter = nodemailer.createTransporter({
    service: 'gmail', // or your email service
    auth: {
        user: process.env.EMAIL_USER, // Your email
        pass: process.env.EMAIL_PASS // Your email password or app password
    }
});

// Alternative configuration for other email services
// const transporter = nodemailer.createTransporter({
//   host: 'smtp.your-email-provider.com',
//   port: 587,
//   secure: false,
//   auth: {
//     user: process.env.EMAIL_USER,
//     pass: process.env.EMAIL_PASS
//   }
// });

// Email sending endpoint
app.post('/api/send-email', async (req, res) => {
    try {
        const {
            formData
        } = req.body;

        // Email to business owner
        const businessEmail = {
            from: process.env.EMAIL_USER,
            to: '<EMAIL>', // Replace with your business email
            subject: `New Query from ${formData.name} - Maa Laxmi Tours`,
            html: generateBusinessEmailHTML(formData)
        };

        // Confirmation email to customer
        const customerEmail = {
            from: process.env.EMAIL_USER,
            to: formData.email,
            subject: 'Thank you for your query - Maa Laxmi Tours',
            html: generateCustomerEmailHTML(formData)
        };

        // Send both emails
        await transporter.sendMail(businessEmail);
        await transporter.sendMail(customerEmail);

        res.status(200).json({
            success: true,
            message: 'Emails sent successfully'
        });

    } catch (error) {
        console.error('Email sending error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to send email',
            error: error.message
        });
    }
});

// Generate HTML for business email
function generateBusinessEmailHTML(formData) {
    return `
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <title>New Query - Maa Laxmi Tours</title>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; }
            .container { max-width: 600px; margin: 0 auto; }
            .header { background: linear-gradient(135deg, #1e40af, #06b6d4); color: white; padding: 30px; text-align: center; }
            .content { background: #f8fafc; padding: 30px; }
            .field { margin-bottom: 20px; }
            .label { font-weight: bold; color: #1e40af; display: block; margin-bottom: 5px; }
            .value { background: white; padding: 12px; border-radius: 6px; border-left: 4px solid #06b6d4; }
            .highlight { background: linear-gradient(135deg, #f59e0b, #f97316); color: white; padding: 15px; border-radius: 8px; margin: 20px 0; text-align: center; }
            .footer { text-align: center; padding: 20px; color: #666; background: #e5e7eb; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>🏝️ New Customer Query</h1>
                <p>Maa Laxmi Tour & Travels</p>
            </div>
            
            <div class="content">
                <div class="highlight">
                    <strong>📧 New query received from ${formData.name}</strong><br>
                    <small>Received on: ${new Date().toLocaleString('en-IN', { timeZone: 'Asia/Kolkata' })}</small>
                </div>

                <div class="field">
                    <span class="label">👤 Customer Name:</span>
                    <div class="value">${formData.name}</div>
                </div>

                <div class="field">
                    <span class="label">📧 Email Address:</span>
                    <div class="value"><a href="mailto:${formData.email}">${formData.email}</a></div>
                </div>

                <div class="field">
                    <span class="label">📱 Phone Number:</span>
                    <div class="value"><a href="tel:${formData.phone}">${formData.phone}</a></div>
                </div>

                <div class="field">
                    <span class="label">👥 Travel Group:</span>
                    <div class="value">${formData.adults} Adult${formData.adults > 1 ? 's' : ''} ${formData.children > 0 ? `+ ${formData.children} Child${formData.children > 1 ? 'ren' : ''}` : ''}</div>
                </div>

                ${formData.travelDate ? `
                <div class="field">
                    <span class="label">📅 Preferred Travel Date:</span>
                    <div class="value">${new Date(formData.travelDate).toLocaleDateString('en-IN')}</div>
                </div>
                ` : ''}

                ${formData.duration ? `
                <div class="field">
                    <span class="label">⏰ Duration:</span>
                    <div class="value">${formData.duration} Days</div>
                </div>
                ` : ''}

                ${formData.budget ? `
                <div class="field">
                    <span class="label">💰 Budget Range:</span>
                    <div class="value">₹${formData.budget} per person</div>
                </div>
                ` : ''}

                ${formData.packageName ? `
                <div class="field">
                    <span class="label">📦 Selected Package:</span>
                    <div class="value">${formData.packageName}</div>
                </div>
                ` : ''}

                ${formData.message ? `
                <div class="field">
                    <span class="label">💬 Special Requirements/Message:</span>
                    <div class="value">${formData.message}</div>
                </div>
                ` : ''}

                <div class="highlight">
                    <strong>⚡ Action Required:</strong><br>
                    Please contact this customer within 24 hours to provide a customized quote and itinerary.
                </div>
            </div>

            <div class="footer">
                <p>This email was sent from the Maa Laxmi Tours website contact form.</p>
                <p><strong>Maa Laxmi Tour & Travels</strong><br>
                Your Trusted Partner for Andaman Adventures</p>
            </div>
        </div>
    </body>
    </html>
  `;
}

// Generate HTML for customer confirmation email
function generateCustomerEmailHTML(formData) {
    return `
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <title>Thank you for your query - Maa Laxmi Tours</title>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; }
            .container { max-width: 600px; margin: 0 auto; }
            .header { background: linear-gradient(135deg, #1e40af, #06b6d4); color: white; padding: 30px; text-align: center; }
            .content { background: #f8fafc; padding: 30px; }
            .welcome { background: linear-gradient(135deg, #10b981, #06b6d4); color: white; padding: 20px; border-radius: 10px; text-align: center; margin-bottom: 30px; }
            .info-box { background: white; padding: 20px; border-radius: 10px; border-left: 5px solid #06b6d4; margin: 20px 0; }
            .contact-info { background: #e0f2fe; padding: 20px; border-radius: 10px; margin: 20px 0; }
            .footer { text-align: center; padding: 20px; color: #666; background: #e5e7eb; }
            .button { display: inline-block; background: linear-gradient(135deg, #f59e0b, #f97316); color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: bold; margin: 10px 0; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>🏝️ Thank You, ${formData.name}!</h1>
                <p>Your Andaman Adventure Awaits</p>
            </div>
            
            <div class="content">
                <div class="welcome">
                    <h2>🎉 Query Received Successfully!</h2>
                    <p>Thank you for choosing Maa Laxmi Tour & Travels for your Andaman adventure.</p>
                </div>

                <div class="info-box">
                    <h3>📋 Your Query Summary:</h3>
                    <ul>
                        <li><strong>Travel Group:</strong> ${formData.adults} Adult${formData.adults > 1 ? 's' : ''} ${formData.children > 0 ? `+ ${formData.children} Child${formData.children > 1 ? 'ren' : ''}` : ''}</li>
                        ${formData.travelDate ? `<li><strong>Preferred Date:</strong> ${new Date(formData.travelDate).toLocaleDateString('en-IN')}</li>` : ''}
                        ${formData.duration ? `<li><strong>Duration:</strong> ${formData.duration} Days</li>` : ''}
                        ${formData.budget ? `<li><strong>Budget:</strong> ₹${formData.budget} per person</li>` : ''}
                        ${formData.packageName ? `<li><strong>Package:</strong> ${formData.packageName}</li>` : ''}
                    </ul>
                </div>

                <div class="info-box">
                    <h3>⏰ What Happens Next?</h3>
                    <ol>
                        <li><strong>Within 24 hours:</strong> Our travel expert will contact you</li>
                        <li><strong>Customized Itinerary:</strong> We'll create a personalized plan based on your preferences</li>
                        <li><strong>Detailed Quote:</strong> You'll receive transparent pricing with no hidden costs</li>
                        <li><strong>Book with Confidence:</strong> 100% satisfaction guarantee</li>
                    </ol>
                </div>

                <div class="contact-info">
                    <h3>📞 Need Immediate Assistance?</h3>
                    <p><strong>Phone:</strong> <a href="tel:+919876543210">+91 98765 43210</a></p>
                    <p><strong>Email:</strong> <a href="mailto:<EMAIL>"><EMAIL></a></p>
                    <p><strong>WhatsApp:</strong> <a href="https://wa.me/919876543210">+91 98765 43210</a></p>
                </div>

                <div style="text-align: center; margin: 30px 0;">
                    <a href="https://maalaxmitours.com" class="button">Visit Our Website</a>
                </div>
            </div>

            <div class="footer">
                <p><strong>Maa Laxmi Tour & Travels</strong><br>
                Your Trusted Partner for Andaman Adventures</p>
                <p>🏆 Award-winning service | 🌊 Local expertise | 💯 100% satisfaction</p>
            </div>
        </div>
    </body>
    </html>
  `;
}

// Health check endpoint
app.get('/api/health', (req, res) => {
    res.json({
        status: 'OK',
        message: 'Email service is running'
    });
});

app.listen(PORT, () => {
    console.log(`Email server running on port ${PORT}`);
});

module.exports = app;